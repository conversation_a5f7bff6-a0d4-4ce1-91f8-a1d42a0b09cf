<?php

/**
 * 重试机制集成测试
 * 验证定时任务的重试机制是否正常工作
 * <AUTHOR> Assistant
 * @version 1.0
 */
class RetryMechanismTest
{
    private $soMdl;
    private $odnMdl;
    private $notificationService;

    public function setUp()
    {
        $this->soMdl = app::get('miele')->model('sap_so');
        $this->odnMdl = app::get('miele')->model('sap_odn');
        $this->notificationService = kernel::single('miele_service_notification');
    }

    /**
     * 测试数据库字段是否正确添加
     */
    public function testRetryCountFieldExists()
    {
        echo "测试重试计数字段是否存在...\n";

        // 检查 sap_so 表的 retry_count 字段
        $soFields = $this->soMdl->db_dump(['id' => 1]);
        if (!isset($soFields['retry_count'])) {
            echo "FAIL: sap_so 表缺少 retry_count 字段\n";
            return false;
        }

        // 检查 sap_odn 表的 retry_count 字段
        $odnFields = $this->odnMdl->db_dump(['id' => 1]);
        if (!isset($odnFields['retry_count'])) {
            echo "FAIL: sap_odn 表缺少 retry_count 字段\n";
            return false;
        }

        echo "PASS: 重试计数字段存在\n";
        return true;
    }

    /**
     * 测试SO单重试机制
     */
    public function testSoRetryMechanism()
    {
        echo "测试SO单重试机制...\n";

        // 创建测试数据
        $testData = [
            'order_bn' => 'TEST-SO-' . time(),
            'sap_sync_status' => 'fail',
            'retry_count' => 3,
            'sap_sync_msg' => '测试重试机制',
            'at_time' => date('Y-m-d H:i:s'),
            'up_time' => date('Y-m-d H:i:s')
        ];

        $testId = $this->soMdl->save($testData);
        if (!$testId) {
            echo "FAIL: 无法创建测试数据\n";
            return false;
        }

        // 测试查询条件
        $retryList = $this->soMdl->getList('id,retry_count', [
            'filter_sql' => "(sap_sync_status = 'pending' OR (sap_sync_status = 'fail' AND retry_count < 5))",
            'id' => $testId
        ]);

        if (empty($retryList)) {
            echo "FAIL: 重试查询条件不正确\n";
            $this->soMdl->delete(['id' => $testId]);
            return false;
        }

        // 测试重试次数更新
        $this->soMdl->update(['retry_count' => 4], ['id' => $testId]);
        $updated = $this->soMdl->db_dump(['id' => $testId]);
        
        if ($updated['retry_count'] != 4) {
            echo "FAIL: 重试次数更新失败\n";
            $this->soMdl->delete(['id' => $testId]);
            return false;
        }

        // 清理测试数据
        $this->soMdl->delete(['id' => $testId]);

        echo "PASS: SO单重试机制测试通过\n";
        return true;
    }

    /**
     * 测试ODN单重试机制
     */
    public function testOdnRetryMechanism()
    {
        echo "测试ODN单重试机制...\n";

        // 创建测试数据
        $testData = [
            'delivery_bn' => 'TEST-ODN-' . time(),
            'sap_add_status' => 'fail',
            'retry_count' => 2,
            'sap_add_msg' => '测试重试机制',
            'at_time' => date('Y-m-d H:i:s'),
            'up_time' => date('Y-m-d H:i:s')
        ];

        $testId = $this->odnMdl->save($testData);
        if (!$testId) {
            echo "FAIL: 无法创建测试数据\n";
            return false;
        }

        // 测试查询条件
        $retryList = $this->odnMdl->getList('id,retry_count', [
            'filter_sql' => "(sap_add_status = 'pending' OR (sap_add_status = 'fail' AND retry_count < 5))",
            'id' => $testId
        ]);

        if (empty($retryList)) {
            echo "FAIL: ODN重试查询条件不正确\n";
            $this->odnMdl->delete(['id' => $testId]);
            return false;
        }

        // 测试重试次数更新
        $this->odnMdl->update(['retry_count' => 5], ['id' => $testId]);
        $updated = $this->odnMdl->db_dump(['id' => $testId]);
        
        if ($updated['retry_count'] != 5) {
            echo "FAIL: ODN重试次数更新失败\n";
            $this->odnMdl->delete(['id' => $testId]);
            return false;
        }

        // 测试超过重试次数的记录不会被查询到
        $overLimitList = $this->odnMdl->getList('id', [
            'filter_sql' => "(sap_add_status = 'pending' OR (sap_add_status = 'fail' AND retry_count < 5))",
            'id' => $testId
        ]);

        if (!empty($overLimitList)) {
            echo "FAIL: 超过重试次数的记录仍被查询到\n";
            $this->odnMdl->delete(['id' => $testId]);
            return false;
        }

        // 清理测试数据
        $this->odnMdl->delete(['id' => $testId]);

        echo "PASS: ODN单重试机制测试通过\n";
        return true;
    }

    /**
     * 测试通知服务集成
     */
    public function testNotificationServiceIntegration()
    {
        echo "测试通知服务集成...\n";

        // 测试任务重试失败通知
        $result = $this->notificationService->sendTaskRetryFailedNotification(
            '测试任务',
            'TEST-001',
            '测试错误信息',
            5
        );

        if (!$result['success']) {
            echo "FAIL: 任务重试失败通知发送失败: " . $result['message'] . "\n";
            return false;
        }

        // 测试SAP回调超时通知
        $result = $this->notificationService->sendSapCallbackTimeoutNotification(
            'SO',
            'TEST-SO-001',
            '25分钟',
            date('Y-m-d H:i:s')
        );

        if (!$result['success']) {
            echo "FAIL: SAP回调超时通知发送失败: " . $result['message'] . "\n";
            return false;
        }

        echo "PASS: 通知服务集成测试通过\n";
        return true;
    }

    /**
     * 测试定时任务类是否存在
     */
    public function testTimerTaskClassExists()
    {
        echo "测试定时任务类是否存在...\n";

        $taskClasses = [
            'miele_autotask_timer_pushso',
            'miele_autotask_timer_pushbucha',
            'miele_autotask_timer_pushodncreate',
            'miele_autotask_timer_pushodncancel',
            'miele_autotask_timer_pushodnconsign',
            'miele_autotask_timer_pushreship',
            'miele_autotask_timer_checksapcallbacks'
        ];

        foreach ($taskClasses as $className) {
            if (!class_exists($className)) {
                echo "FAIL: 定时任务类 $className 不存在\n";
                return false;
            }

            $instance = new $className();
            if (!method_exists($instance, 'process')) {
                echo "FAIL: 定时任务类 $className 缺少 process 方法\n";
                return false;
            }
        }

        echo "PASS: 所有定时任务类存在且结构正确\n";
        return true;
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始运行重试机制集成测试...\n\n";

        $tests = [
            'testRetryCountFieldExists',
            'testSoRetryMechanism',
            'testOdnRetryMechanism',
            'testNotificationServiceIntegration',
            'testTimerTaskClassExists'
        ];

        $passed = 0;
        $total = count($tests);

        foreach ($tests as $test) {
            echo "运行测试: $test\n";
            if ($this->$test()) {
                $passed++;
            }
            echo "\n";
        }

        echo "集成测试完成: $passed/$total 通过\n";
        return $passed === $total;
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new RetryMechanismTest();
    $test->setUp();
    $test->runAllTests();
}
