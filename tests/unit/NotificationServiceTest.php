<?php

/**
 * Miele 通知服务单元测试
 * <AUTHOR> Assistant
 * @version 1.0
 */
class NotificationServiceTest
{
    private $notificationService;

    public function setUp()
    {
        $this->notificationService = kernel::single('miele_service_notification');
    }

    /**
     * 测试模板存在性检查
     */
    public function testTemplateExists()
    {
        // 测试已存在的模板
        $exists = $this->notificationService->templateExists('miele_task_retry_failed');
        if (!$exists) {
            echo "FAIL: 模板 miele_task_retry_failed 应该存在\n";
            return false;
        }

        // 测试不存在的模板
        $notExists = $this->notificationService->templateExists('non_existent_template');
        if ($notExists) {
            echo "FAIL: 不存在的模板不应该返回true\n";
            return false;
        }

        echo "PASS: 模板存在性检查测试通过\n";
        return true;
    }

    /**
     * 测试获取可用模板列表
     */
    public function testGetAvailableTemplates()
    {
        $templates = $this->notificationService->getAvailableTemplates();
        
        if (!is_array($templates)) {
            echo "FAIL: 获取模板列表应该返回数组\n";
            return false;
        }

        // 检查是否包含我们新增的模板
        $templateBns = array_column($templates, 'template_bn');
        if (!in_array('miele_task_retry_failed', $templateBns)) {
            echo "FAIL: 模板列表应该包含 miele_task_retry_failed\n";
            return false;
        }

        if (!in_array('miele_sap_callback_timeout', $templateBns)) {
            echo "FAIL: 模板列表应该包含 miele_sap_callback_timeout\n";
            return false;
        }

        echo "PASS: 获取可用模板列表测试通过\n";
        return true;
    }

    /**
     * 测试任务重试失败通知
     */
    public function testSendTaskRetryFailedNotification()
    {
        $result = $this->notificationService->sendTaskRetryFailedNotification(
            'pushso',
            'SO-123456',
            '网络连接超时',
            5
        );

        if (!is_array($result)) {
            echo "FAIL: 发送通知应该返回数组\n";
            return false;
        }

        if (!isset($result['success'])) {
            echo "FAIL: 返回结果应该包含 success 字段\n";
            return false;
        }

        if (!isset($result['message'])) {
            echo "FAIL: 返回结果应该包含 message 字段\n";
            return false;
        }

        echo "PASS: 任务重试失败通知测试通过\n";
        echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        return true;
    }

    /**
     * 测试SAP回调超时通知
     */
    public function testSendSapCallbackTimeoutNotification()
    {
        $result = $this->notificationService->sendSapCallbackTimeoutNotification(
            'SO',
            'SO-123456',
            '25分钟',
            '2025-08-04 10:30:00'
        );

        if (!is_array($result)) {
            echo "FAIL: 发送通知应该返回数组\n";
            return false;
        }

        if (!isset($result['success'])) {
            echo "FAIL: 返回结果应该包含 success 字段\n";
            return false;
        }

        if (!isset($result['message'])) {
            echo "FAIL: 返回结果应该包含 message 字段\n";
            return false;
        }

        echo "PASS: SAP回调超时通知测试通过\n";
        echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        return true;
    }

    /**
     * 测试通用通知发送
     */
    public function testSendNotification()
    {
        $templateVars = [
            'task_name' => '测试任务',
            'key_data' => 'TEST-001',
            'error_message' => '测试错误',
            'retry_count' => 3
        ];

        $result = $this->notificationService->sendNotification(
            'miele_task_retry_failed',
            $templateVars
        );

        if (!is_array($result)) {
            echo "FAIL: 发送通知应该返回数组\n";
            return false;
        }

        if (!isset($result['success']) || !isset($result['message'])) {
            echo "FAIL: 返回结果格式不正确\n";
            return false;
        }

        echo "PASS: 通用通知发送测试通过\n";
        echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        return true;
    }

    /**
     * 测试不存在模板的处理
     */
    public function testSendNotificationWithInvalidTemplate()
    {
        $result = $this->notificationService->sendNotification(
            'non_existent_template',
            ['test' => 'value']
        );

        if (!is_array($result)) {
            echo "FAIL: 发送通知应该返回数组\n";
            return false;
        }

        if ($result['success'] !== false) {
            echo "FAIL: 不存在的模板应该返回失败\n";
            return false;
        }

        if (strpos($result['message'], '通知模板不存在') === false) {
            echo "FAIL: 错误消息应该包含模板不存在的提示\n";
            return false;
        }

        echo "PASS: 不存在模板处理测试通过\n";
        return true;
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始运行 NotificationService 单元测试...\n\n";

        $tests = [
            'testTemplateExists',
            'testGetAvailableTemplates',
            'testSendTaskRetryFailedNotification',
            'testSendSapCallbackTimeoutNotification',
            'testSendNotification',
            'testSendNotificationWithInvalidTemplate'
        ];

        $passed = 0;
        $total = count($tests);

        foreach ($tests as $test) {
            echo "运行测试: $test\n";
            if ($this->$test()) {
                $passed++;
            }
            echo "\n";
        }

        echo "测试完成: $passed/$total 通过\n";
        return $passed === $total;
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new NotificationServiceTest();
    $test->setUp();
    $test->runAllTests();
}
