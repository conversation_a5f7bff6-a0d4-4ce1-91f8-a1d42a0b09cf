# 任务重试与回调监控优化 - 任务分解

## Document Information

- **Feature Name**: 任务重试与回调监控优化
- **Version**: 1.0
- **Date**: 2025-08-04
- **Author**: AI Assistant
- **Related Documents**: 
  - Requirements: docs/specs/taskRetryOptimization/optimized_prompt.md
  - Design: docs/specs/taskRetryOptimization/design.md

## Implementation Overview

本实施计划将分三个阶段完成：第一阶段实现统一通知服务，第二阶段为现有定时任务增加重试机制，第三阶段开发SAP回调监控任务。每个阶段都包含完整的开发、测试和验证流程。

### Implementation Strategy
- 基于现有 monitor 模块构建通知服务，确保兼容性
- 渐进式修改现有定时任务，降低风险
- 采用数据库字段扩展方式实现重试计数
- 使用独立的监控任务实现回调检查

### Development Approach
- **Testing Strategy**: 单元测试 + 集成测试
- **Integration Strategy**: 基于现有架构逐步集成
- **Deployment Strategy**: 分阶段部署，确保系统稳定性

## Implementation Plan

### Phase 1: 统一通知服务实现

- [ ] 1. 分析现有通知模板系统
  - 研究 miele_service_event_template_get 的实现
  - 查看 app/miele/initial/miele.event_template.sql 中的模板结构
  - 分析 monitor 模块的事件通知机制
  - _Requirements: 第一阶段步骤1_

- [ ] 2. 创建统一通知服务类
  - 创建 app/miele/lib/service/notification.php
  - 实现 sendNotification 方法
  - 集成 monitor_event_notify 和 console_email 服务
  - 添加模板变量替换功能
  - _Requirements: 第一阶段步骤2_

- [ ] 3. 添加新的通知模板
  - 创建任务重试失败通知模板
  - 创建SAP回调超时通知模板
  - 更新 app/miele/initial/miele.event_template.sql
  - _Requirements: 第一阶段步骤2_

- [ ] 4. 编写通知服务单元测试
  - 测试模板获取功能
  - 测试变量替换功能
  - 测试邮件发送功能
  - 测试错误处理逻辑
  - _Requirements: 第一阶段验证_

### Phase 2: 定时任务重试机制实现

- [ ] 5. 数据库表结构修改
  - 为 sdb_miele_sap_so 表添加 retry_count 字段
  - 为 sdb_miele_sap_odn 表添加 retry_count 字段
  - 创建数据库迁移脚本
  - _Requirements: 第二阶段修改逻辑步骤1_

- [ ] 6. 修改 pushso.php 定时任务
  - 修改数据获取条件：pending 或 (fail 且 retry_count < 5)
  - 添加 try-catch 异常处理
  - 实现重试计数逻辑
  - 集成通知服务发送失败告警
  - _Requirements: 第二阶段修改逻辑_

- [ ] 7. 修改 pushbucha.php 定时任务
  - 修改数据获取条件
  - 添加异常处理和重试逻辑
  - 集成通知服务
  - _Requirements: 第二阶段修改逻辑_

- [ ] 8. 修改 pushodncancel.php 定时任务
  - 修改数据获取条件
  - 添加异常处理和重试逻辑
  - 集成通知服务
  - _Requirements: 第二阶段修改逻辑_

- [ ] 9. 修改 pushodnconsign.php 定时任务
  - 修改数据获取条件
  - 添加异常处理和重试逻辑
  - 集成通知服务
  - _Requirements: 第二阶段修改逻辑_

- [ ] 10. 修改 pushodncreate.php 定时任务
  - 修改数据获取条件
  - 添加异常处理和重试逻辑
  - 集成通知服务
  - _Requirements: 第二阶段修改逻辑_

- [ ] 11. 修改 pushreship.php 定时任务
  - 修改数据获取条件
  - 添加异常处理和重试逻辑
  - 集成通知服务
  - _Requirements: 第二阶段修改逻辑_

### Phase 3: SAP回调监控任务开发

- [ ] 12. 创建SAP回调检查任务
  - 创建 app/miele/lib/autotask/timer/checksapcallbacks.php
  - 实现 process 主方法
  - 添加任务锁机制
  - _Requirements: 第三阶段步骤1-2_

- [ ] 13. 实现 checkSoCallbacks 方法
  - 查询超时的 sap_so 记录（20分钟无回调）
  - 基于 sap_sync_time 和当前时间判断超时
  - 调用通知服务发送超时告警
  - _Requirements: 第三阶段步骤3_

- [ ] 14. 实现 checkOdnCallbacks 方法
  - 查询超时的 sap_odn 记录（20分钟无ODN号返回）
  - 基于 sap_add_status 和 up_time 判断超时
  - 调用通知服务发送超时告警
  - _Requirements: 第三阶段步骤4_

- [ ] 15. 配置定时任务执行
  - 配置任务每分钟执行一次
  - 添加任务到系统定时任务列表
  - _Requirements: 第三阶段步骤1_

### Phase 4: 测试和验证

- [ ] 16. 集成测试
  - 测试通知服务与各定时任务的集成
  - 测试重试机制的完整流程
  - 测试回调监控的超时检测
  - _Requirements: 全阶段验证_

- [ ] 17. 端到端测试
  - 模拟任务失败场景测试重试机制
  - 模拟SAP回调超时场景测试监控告警
  - 验证邮件通知的正确发送
  - _Requirements: 全阶段验证_

- [ ] 18. 性能测试
  - 测试定时任务的执行性能
  - 测试数据库查询的性能影响
  - 验证系统负载的可接受性
  - _Requirements: 性能要求验证_

### Phase 5: 文档和部署

- [ ] 19. 创建参考文档
  - 编写 docs/reference/miele-task-retry-callback-monitor-reference.md
  - 记录重试机制的使用方法
  - 记录回调监控的配置说明
  - _Requirements: 文档要求_

- [ ] 20. 部署准备和验证
  - 准备生产环境部署脚本
  - 执行最终的功能验证
  - 清理临时代码和注释
  - _Requirements: 部署要求_

---

## Task Execution Guidelines

### 开发规范遵循
- 严格按照 docs/cheatsheet/system/class-loading-rules.md 创建类文件
- 使用 docs/cheatsheet/database/query-basic.md 中的数据库操作方法
- 遵循现有的代码风格和命名规范

### 测试要求
- 每个功能模块都需要编写单元测试
- 重要的业务逻辑需要集成测试覆盖
- 所有修改都需要进行回归测试

### 质量检查
- 代码审查确保符合项目规范
- 功能测试验证需求实现
- 性能测试确保系统稳定性
