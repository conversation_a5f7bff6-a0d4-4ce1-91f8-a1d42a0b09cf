# 任务重试与回调监控优化 - 技术设计文档

---

## Document Information

- **Feature Name**: 任务重试与回调监控优化
- **Version**: 1.0
- **Date**: 2025-08-04
- **Author**: AI Assistant
- **Reviewers**: 待定
- **Related Documents**: docs/specs/taskRetryOptimization/optimized_prompt.md

## Overview

本设计文档描述了如何为 Miele 系统的定时任务增加重试机制，并实现 SAP 回调监控功能。该设计基于现有的 monitor 模块通知系统，创建统一的通知服务，并为定时任务增加可靠的重试机制。

### Design Goals
- 创建统一的通知服务，支持邮件和系统消息发送
- 为现有定时任务增加可配置的重试机制
- 实现 SAP 回调超时监控和告警
- 保持与现有系统架构的兼容性

### Key Design Decisions
- 复用现有的 monitor 模块事件模板系统进行通知
- 在数据表中增加 retry_count 字段实现重试计数
- 使用统一的通知服务接口，便于后续扩展
- 采用定时任务方式实现回调监控，降低系统复杂度

## Architecture

### System Context
该功能主要涉及以下系统组件：
- Miele 定时任务系统
- Monitor 事件通知系统
- SAP 数据同步系统
- 邮件发送系统

```mermaid
graph TB
    A[定时任务] --> B[通知服务]
    B --> C[Monitor事件系统]
    C --> D[邮件发送]
    A --> E[SAP数据表]
    F[回调监控任务] --> B
    F --> E
```

### High-Level Architecture
系统采用三层架构设计：

```mermaid
graph LR
    A[定时任务层] --> B[业务逻辑层]
    B --> C[数据访问层]
    B --> D[通知服务层]
    D --> E[邮件发送层]
```

### Technology Stack
| Layer | Technology | Rationale |
|-------|------------|-----------|
| 定时任务 | PHP Cron Jobs | 现有系统架构 |
| 业务逻辑 | PHP Classes | 符合项目规范 |
| 数据存储 | MySQL | 现有数据库系统 |
| 通知系统 | Monitor模块 | 复用现有通知基础设施 |

## Components and Interfaces

### Component 1: 统一通知服务 (NotificationService)

**Purpose**: 提供标准化的通知发送接口，支持邮件和系统消息

**Responsibilities**:
- 根据模板ID获取通知模板
- 渲染通知内容（替换模板变量）
- 调用底层邮件发送服务
- 记录通知发送日志

**Interfaces**:
- **Input**: template_id, template_vars
- **Output**: 发送结果状态
- **Dependencies**: monitor_event_notify, console_email

**Implementation Notes**:
- 类名: miele_service_notification
- 文件位置: app/miele/lib/service/notification.php
- 复用现有的 monitor 模块事件模板系统

### Component 2: 重试机制增强 (RetryEnhancement)

**Purpose**: 为现有定时任务增加重试机制

**Responsibilities**:
- 在数据表中维护重试计数
- 实现重试逻辑和失败处理
- 调用通知服务发送失败告警

**Interfaces**:
- **Input**: 任务执行结果
- **Output**: 重试决策和通知触发
- **Dependencies**: 各定时任务类, NotificationService

**Implementation Notes**:
- 修改现有定时任务文件
- 增加 retry_count 字段到相关数据表
- 最大重试次数配置为 5 次

### Component 3: SAP回调监控任务 (SapCallbackMonitor)

**Purpose**: 监控 SAP 系统回调状态，发现超时情况

**Responsibilities**:
- 检查 SO 单回调超时（20分钟）
- 检查 ODN 单回调超时（20分钟）
- 发送超时告警通知

**Interfaces**:
- **Input**: 定时任务触发
- **Output**: 超时告警通知
- **Dependencies**: sap_so表, sap_odn表, NotificationService

**Implementation Notes**:
- 类名: miele_autotask_timer_checksapcallbacks
- 文件位置: app/miele/lib/autotask/timer/checksapcallbacks.php
- 执行频率: 每分钟一次

## Data Models

### 重试计数字段增强

需要在以下表中增加 retry_count 字段：

```sql
-- sap_so 表增加重试字段
ALTER TABLE sdb_miele_sap_so ADD COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数';

-- sap_odn 表增加重试字段  
ALTER TABLE sdb_miele_sap_odn ADD COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数';
```

### 通知模板数据

```sql
-- 任务重试失败通知模板
INSERT INTO sdb_monitor_event_template (
    template_bn, template_name, event_type, send_type, content, status, source
) VALUES (
    'miele_task_retry_failed', 
    '定时任务重试失败通知', 
    'miele_task_retry_failed', 
    'email', 
    '**定时任务重试失败通知**\n> 任务名称：{task_name}\n> 关键数据：{key_data}\n> 失败原因：{error_message}\n> 重试次数：{retry_count}', 
    1, 
    'system'
);

-- SAP回调超时通知模板
INSERT INTO sdb_monitor_event_template (
    template_bn, template_name, event_type, send_type, content, status, source  
) VALUES (
    'miele_sap_callback_timeout',
    'SAP回调超时通知',
    'miele_sap_callback_timeout', 
    'email',
    '**SAP回调超时通知**\n> 单据类型：{doc_type}\n> 单据号：{doc_number}\n> 超时时长：{timeout_duration}\n> 请求时间：{request_time}',
    1,
    'system'
);
```

### Data Flow

```mermaid
sequenceDiagram
    participant Timer as 定时任务
    participant Logic as 业务逻辑
    participant DB as 数据库
    participant Notify as 通知服务
    
    Timer->>Logic: 执行任务
    Logic->>DB: 查询待处理数据
    DB-->>Logic: 返回数据列表
    Logic->>Logic: 处理业务逻辑
    alt 处理失败
        Logic->>DB: 增加重试计数
        Logic->>Logic: 判断重试次数
        alt 超过最大重试次数
            Logic->>Notify: 发送失败通知
        end
    end
```

## 参考资料

- **参考的 Cheatsheet**: 
  - docs/cheatsheet/system/class-loading-rules.md
  - docs/cheatsheet/database/query-basic.md
- **参考的 Reference 文档**: 
  - docs/reference/miele-odn-interface-reference.md
  - docs/reference/miele-bucha-system-reference.md
- **参考的 URL**: 无

## API Design

### NotificationService 接口

**Method**: `sendNotification`
**Purpose**: 发送通知消息

**Parameters**:
```php
public function sendNotification($templateId, $templateVars = [], $receivers = [])
```

**Return**:
```php
[
    'success' => true/false,
    'message' => '发送结果描述',
    'notify_id' => '通知记录ID'
]
```

## Error Handling

### Error Categories
| Category | Description | User Action |
|----------|-------------|-------------|
| Template Not Found | 通知模板不存在 | 检查模板配置 |
| Email Config Error | 邮件配置错误 | 检查邮件服务器配置 |
| Database Error | 数据库操作失败 | 检查数据库连接 |
| Retry Limit Exceeded | 重试次数超限 | 人工介入处理 |

### Logging Strategy
- **Error Logs**: 记录所有异常和错误信息
- **Audit Logs**: 记录重试操作和通知发送
- **Performance Logs**: 记录任务执行时间

## Performance Considerations

### Expected Load
- **定时任务频率**: 每5分钟执行一次
- **回调监控频率**: 每分钟执行一次
- **通知发送量**: 预计每天不超过100条

### Optimization Strategies
- 使用数据库索引优化查询性能
- 批量处理减少数据库连接次数
- 异步发送通知避免阻塞主流程

## Testing Strategy

### Unit Testing
- **Coverage Target**: 80%
- **Testing Framework**: PHPUnit
- **Key Test Areas**: 重试逻辑、通知发送、超时检查

### Integration Testing
- **API Testing**: 通知服务接口测试
- **Database Testing**: 重试计数字段测试
- **Email Testing**: 邮件发送功能测试

## 产出评估

- **是否需要生成新的 Cheatsheet?**: 否
  - **理由**: 本次开发主要是在现有架构基础上增加功能，没有引入新的通用设计模式

- **是否需要生成 Reference 文档?**: 是
  - **理由**: 重试机制和回调监控是重要的系统功能，需要作为参考文档永久存档
  - **目录**: docs/reference/miele-task-retry-callback-monitor-reference.md
  - **任务阶段**: 在实施阶段创建
