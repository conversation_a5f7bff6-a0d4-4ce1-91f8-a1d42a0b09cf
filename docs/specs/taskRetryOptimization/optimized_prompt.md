# 任务重试与回调监控优化执行计划

## 第一阶段：实现统一通知服务

**目标**：创建一个标准化的通知服务，用于发送邮件和系统消息。此服务将在后续阶段中用于发送失败和超时告警。

**执行步骤**：
1.  **分析现有模板**：
    *   研究 `miele_service_event_template_get` 的实现，理解如何根据模板ID获取和渲染通知内容。
    *   查看 `app/miele/initial/miele.event_template.sql`，熟悉已有的通知模板，特别是系统管理员通知相关的模板。
2.  **创建或封装通知功能**：
    *   在 `app/miele/lib/` 目录下创建一个新的服务类（例如 `NotificationService.php`）或在现有服务中添加一个公共方法。
    *   该功能应接收 `template_id` 和 `template_vars` (模板变量)作为参数，并处理实际的邮件和消息发送逻辑。

## 第二阶段：为定时任务增加重试机制

**目标**：修改指定的定时任务，使其在执行失败时能够自动重试。如果重试超过5次后仍然失败，则调用第一阶段创建的通知服务发送警报。

**执行步骤**：
对于以下每个PHP文件，执行相同的修改逻辑：
*   `app/miele/lib/autotask/timer/pushso.php`
*   `app/miele/lib/autotask/timer/pushbucha.php`
*   `app/miele/lib/autotask/timer/pushodncancel.php`
*   `app/miele/lib/autotask/timer/pushodnconsign.php`
*   `app/miele/lib/autotask/timer/pushodncreate.php`
*   `app/miele/lib/autotask/timer/pushreship.php`

**修改逻辑**：
1.  **引入重试计数字段**：
    *   在相关数据表中增加一个 `retry_count` 字段来跟踪重试次数，该字段需要持久化存储。
    *   重试次数的最大值配置为 5 次。
2.  **修改数据获取条件**：
    *   获取需要推送的数据条件应该为：
        *   状态为 `pending`（待推送），或者
        *   状态为 `fail`（推送失败）并且 `retry_count < 5`
3.  **实现核心重试逻辑**：
    *   在任务执行的核心代码块外层包裹 `try...catch` 语句。
    *   在 `catch` 块中，递增 `retry_count` 字段并更新到数据库。
    *   **判断重试次数**：
        *   如果 `retry_count >= 5`，则认为任务最终失败。调用通知服务，向系统管理员发送一条明确的失败通知，内容应包含任务名称、关键数据（如订单号）和失败原因。
        *   如果 `retry_count < 5`，则将状态更新为 `fail`，等待下次定时任务重新处理。

## 第三阶段：开发SAP回调检查任务

**目标**：创建一个新的定时任务，每分钟执行一次，用于监控SAP系统的回调状态，并在超时后发送警报。

**执行步骤**：
1.  **创建新任务文件**：
    *   在 `app/miele/lib/autotask/timer/` 目录下创建新文件，例如 `checksapcallbacks.php`。
    *   配置该任务，确保其执行频率为每分钟一次。
2.  **实现主进程方法**：
    *   在任务类中定义 `process()` 方法，该方法将按顺序调用以下两个独立的检查方法。
3.  **开发 `checkSoCallbacks()` 方法**：
    *   **查询逻辑**：筛选出已成功请求SAP但长时间未收到回调的 `sap_so` 单据。
    *   **判断条件**：`当前时间 - 请求发送时间 > 20分钟`。
    *   **触发操作**：对每一个满足条件的单据，调用通知服务，向系统管理员发送超时警报。
4.  **开发 `checkOdnCallbacks()` 方法**：
    *   **查询逻辑**：筛选出已成功创建发货单但未返回ODN单号的记录。
    *   **判断条件**：基于 `sap_add_status` 字段和 `up_time` 字段，`当前时间 - up_time > 20分钟`。
    *   **触发操作**：对每一个满足条件的记录，调用通知服务，向系统管理员发送超时警报。
