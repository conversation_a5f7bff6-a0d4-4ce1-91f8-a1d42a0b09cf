# ERPAPI WMS 发货单参数格式化 Service

## 概述

本文档描述了 ERPAPI WMS 发货单参数格式化 Service 的使用方法，该 Service 用于处理不同 APP 的 `delivery_create` 参数格式化差异。

## Service 注册

Service 应该在具体的 APP 中注册，而不是在 erpapi 中。例如：

### miele APP 中的注册 (`app/miele/services.xml`)：

```xml
<!-- Miele APP WMS 发货单参数格式化 Service -->
<service id="erpapi.service.wms.delivery.params.format">
    <class>miele_service_erpapi_wms_delivery</class>
</service>
```

### 其他 APP 中的注册：

每个 APP 可以根据自己的需求实现不同的格式化逻辑，并在自己的 services.xml 中注册。

**重要说明：**
- Service 的注册应该在具体的 APP 中，而不是在 erpapi 中
- 这样可以避免不同 APP 之间的 service 冲突
- 每个 APP 可以根据自己的需求实现不同的格式化逻辑

## 使用方法

### 1. 在 delivery_create 中的调用

在 `app/erpapi/lib/wms/request/delivery.php` 的 `delivery_create` 方法中已经添加了 service 调用：

```php
// 通过 service 处理不同 APP 的参数格式化
$params = $this->_format_delivery_create_params($sdf);

// 调用 service 进行参数扩展或修改
if ($service = kernel::servicelist('erpapi.service.wms.delivery.format_params')) {
    foreach ($service as $object => $instance) {
        if (method_exists($instance, 'format_delivery_create_params')) {
            $params = $instance->format_delivery_create_params($sdf, $params, $this->__channelObj);
        }
    }
}
```

### 2. Service 实现

Service 实现文件位于：`app/erpapi/lib/service/wms/delivery/format_params.php`

主要方法：
- `format_delivery_create_params($sdf, $params, $channelObj)` - 主要的格式化方法

## 扩展方式

### 1. 针对特定渠道的参数调整

```php
public function format_delivery_create_params($sdf, $params, $channelObj)
{
    $shop_type = $sdf['shop_type'];
    
    // 淘宝渠道特殊处理
    if ($shop_type == 'taobao') {
        $params = $this->_format_taobao_params($sdf, $params);
    }
    
    // 京东渠道特殊处理
    elseif ($shop_type == 'jd') {
        $params = $this->_format_jd_params($sdf, $params);
    }
    
    return $params;
}
```

### 2. 针对特定 WMS 渠道的参数调整

```php
public function format_delivery_create_params($sdf, $params, $channelObj)
{
    $channel_id = $channelObj->wms['channel_id'];
    
    // 特定 WMS 渠道处理
    if ($channel_id == 'specific_wms_channel') {
        $params = $this->_format_specific_wms_params($sdf, $params);
    }
    
    return $params;
}
```

### 3. 添加新的格式化方法

```php
private function _format_custom_params($sdf, $params)
{
    // 自定义格式化逻辑
    if (isset($sdf['custom_field'])) {
        $params['custom_field'] = $sdf['custom_field'];
    }
    
    return $params;
}
```

## 参数说明

### 输入参数

- `$sdf` - 发货单数据，包含所有原始发货单信息
- `$params` - 已经过基础格式化的参数数组
- `$channelObj` - 渠道对象，包含渠道配置信息

### 返回参数

- `array` - 修改后的参数数组，将用于 WMS 接口调用

## 注意事项

1. **保持原有逻辑**：Service 应该是对原有逻辑的扩展，而不是替换
2. **参数验证**：在修改参数前应该进行必要的验证
3. **错误处理**：应该包含适当的错误处理机制
4. **性能考虑**：避免在 Service 中进行耗时的操作

## 示例场景

### 场景1：淘宝渠道特殊字段

```php
private function _format_taobao_params($sdf, $params)
{
    // 添加淘宝特有的字段
    if (isset($sdf['taobao_order_id'])) {
        $params['taobao_order_id'] = $sdf['taobao_order_id'];
    }
    
    // 修改收货人信息格式
    if (isset($params['receiver_name'])) {
        $params['receiver_name'] = $this->_format_taobao_receiver_name($params['receiver_name']);
    }
    
    return $params;
}
```

### 场景2：特定 WMS 渠道字段调整

```php
private function _format_specific_wms_params($sdf, $params)
{
    // 调整商品信息格式
    if (isset($params['items'])) {
        $items = json_decode($params['items'], true);
        $items = $this->_format_specific_wms_items($items);
        $params['items'] = json_encode($items);
    }
    
    // 添加 WMS 特有字段
    $params['wms_specific_field'] = $sdf['wms_specific_value'];
    
    return $params;
}
```

## 调试和测试

### 1. 启用调试日志

```php
public function format_delivery_create_params($sdf, $params, $channelObj)
{
    // 添加调试日志
    logger::info('WMS delivery format params - Original params: ' . json_encode($params));
    
    // 执行格式化逻辑
    $modified_params = $this->_do_format($sdf, $params, $channelObj);
    
    logger::info('WMS delivery format params - Modified params: ' . json_encode($modified_params));
    
    return $modified_params;
}
```

### 2. 单元测试

建议为每个格式化方法编写单元测试，确保参数修改的正确性。

## 版本历史

- v1.0 - 初始版本，支持基本的参数格式化功能
- v1.1 - 添加了特定渠道的处理逻辑
- v1.2 - 优化了错误处理和日志记录 