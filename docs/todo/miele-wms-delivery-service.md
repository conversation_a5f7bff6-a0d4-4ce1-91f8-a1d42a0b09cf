# Miele APP WMS 发货单参数格式化 Service

## 概述

本文档描述了 Miele APP 中 WMS 发货单参数格式化 Service 的实现和使用方法，该 Service 用于处理 miele APP 的 `delivery_create` 参数格式化差异。

## 文件结构

```
app/miele/
├── lib/
│   └── service/
│       └── erpapi/
│           └── wms/
│               └── delivery.php          # Service 实现文件
└── services.xml                          # Service 注册文件
```

## Service 注册

Service 已在 `app/miele/services.xml` 中注册：

```xml
<!-- Miele APP WMS 发货单参数格式化 Service -->
<service id="erpapi.service.wms.delivery.params.format">
    <class>miele_service_erpapi_wms_delivery</class>
</service>
```

**重要说明：**
- Service 的注册应该在具体的 APP 中（如 miele APP），而不是在 erpapi 中
- 这样可以避免不同 APP 之间的 service 冲突
- 每个 APP 可以根据自己的需求实现不同的格式化逻辑

## Service 实现

### 主要方法

#### `format_delivery_create_params($sdf, $params, $channelObj)`

主要的格式化方法，根据不同的渠道和配置进行参数调整。

**参数说明：**
- `$sdf` - 发货单数据，包含所有原始发货单信息
- `$params` - 已经过基础格式化的参数数组
- `$channelObj` - 渠道对象，包含渠道配置信息

**返回：**
- `array` - 修改后的参数数组

### 私有方法

#### `_format_miele_specific_params($sdf, $params)`
处理 miele APP 通用的特殊逻辑，包括：
- 添加 miele 特有的字段
- 处理收货人信息格式
- 处理商品信息

#### `_format_miele_shop_params($sdf, $params)`
处理 miele 店铺渠道的特殊逻辑，包括：
- 添加店铺特有的字段
- 处理订单来源信息

#### `_format_miele_wms_params($sdf, $params)`
处理 miele WMS 渠道的特殊逻辑，包括：
- 调整字段名称或格式
- 添加 WMS 特有的字段

## 使用场景

### 场景1：添加 miele 特有字段

```php
private function _format_miele_specific_params($sdf, $params)
{
    // 添加 miele 特有的字段
    if (isset($sdf['miele_order_type'])) {
        $params['miele_order_type'] = $sdf['miele_order_type'];
    }
    
    return $params;
}
```

### 场景4：添加 inventoryType 字段

```php
private function _format_miele_items($items)
{
    // 获取仓库信息
    $branchObj = app::get('ome')->model('branch');
    
    foreach ($items['item'] as $key => $item) {
        // 添加 inventoryType 字段，取值为 storage_code
        if (isset($item['branch_bn'])) {
            $branchInfo = $branchObj->dump(array('branch_bn' => $item['branch_bn']), 'storage_code');
            if ($branchInfo && $branchInfo['storage_code']) {
                $items['item'][$key]['inventoryType'] = $branchInfo['storage_code'];
            }
        }
    }
    
    return $items;
}
```

**说明：**
- `inventoryType` 字段的值来源于 `branch` 表的 `storage_code` 字段
- 通过 `branch_bn` 关联查询获取对应的 `storage_code`
- 只有当 `branch_bn` 存在且查询到对应的 `storage_code` 时才会添加该字段

### 场景2：处理收货人信息格式

```php
private function _format_miele_receiver_name($name)
{
    // miele APP 收货人姓名特殊处理逻辑
    $name = trim($name);
    $name = preg_replace('/\s+/', ' ', $name);
    
    return $name;
}
```

### 场景3：处理商品信息

```php
private function _format_miele_items($items)
{
    // 获取仓库信息
    $branchObj = app::get('ome')->model('branch');
    
    foreach ($items['item'] as $key => $item) {
        // 处理商品编码
        if (isset($item['item_code'])) {
            $items['item'][$key]['item_code'] = $this->_format_miele_item_code($item['item_code']);
        }
        
        // 添加 inventoryType 字段，取值为 storage_code
        if (isset($item['branch_bn'])) {
            $branchInfo = $branchObj->dump(array('branch_bn' => $item['branch_bn']), 'storage_code');
            if ($branchInfo && $branchInfo['storage_code']) {
                $items['item'][$key]['inventoryType'] = $branchInfo['storage_code'];
            }
        }
        
        // 添加 miele 特有的商品字段
        $items['item'][$key]['miele_item_type'] = 'standard';
    }
    
    return $items;
}
```

## 扩展方式

### 1. 添加新的渠道处理逻辑

```php
public function format_delivery_create_params($sdf, $params, $channelObj)
{
    $shop_type = $sdf['shop_type'];
    
    // 添加新的渠道处理
    if ($shop_type == 'new_miele_channel') {
        $params = $this->_format_new_miele_channel_params($sdf, $params);
    }
    
    return $params;
}

private function _format_new_miele_channel_params($sdf, $params)
{
    // 新渠道的特殊处理逻辑
    return $params;
}
```

### 2. 添加新的字段处理逻辑

```php
private function _format_miele_specific_params($sdf, $params)
{
    // 添加新的字段处理
    if (isset($sdf['new_miele_field'])) {
        $params['new_miele_field'] = $this->_format_new_miele_field($sdf['new_miele_field']);
    }
    
    return $params;
}

private function _format_new_miele_field($value)
{
    // 新字段的处理逻辑
    return $value;
}
```

## 调试和测试

### 1. 启用调试日志

```php
public function format_delivery_create_params($sdf, $params, $channelObj)
{
    // 添加调试日志
    logger::info('Miele WMS delivery format params - Original params: ' . json_encode($params));
    
    // 执行格式化逻辑
    $modified_params = $this->_do_format($sdf, $params, $channelObj);
    
    logger::info('Miele WMS delivery format params - Modified params: ' . json_encode($modified_params));
    
    return $modified_params;
}
```

### 2. 单元测试

建议为每个格式化方法编写单元测试，确保参数修改的正确性。

## 注意事项

1. **保持原有逻辑**：Service 应该是对原有逻辑的扩展，而不是替换
2. **参数验证**：在修改参数前应该进行必要的验证
3. **错误处理**：应该包含适当的错误处理机制
4. **性能考虑**：避免在 Service 中进行耗时的操作
5. **命名规范**：遵循 miele APP 的命名规范

## 版本历史

- v1.0 - 初始版本，支持 miele APP 基本的参数格式化功能
- v1.1 - 添加了特定渠道的处理逻辑
- v1.2 - 优化了错误处理和日志记录

## 相关文件

- `app/erpapi/lib/wms/request/delivery.php` - WMS 发货单请求类
- `app/miele/lib/service/erpapi/wms/delivery.php` - miele APP Service 实现
- `app/miele/services.xml` - miele APP Service 注册文件 