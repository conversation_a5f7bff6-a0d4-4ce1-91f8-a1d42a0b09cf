# Miele 任务重试与回调监控系统参考文档

## 概述

本文档描述了 Miele 系统中任务重试机制和 SAP 回调监控功能的实现细节、使用方法和维护指南。

## 系统架构

### 核心组件

1. **统一通知服务** (`miele_service_notification`)
   - 提供标准化的通知发送接口
   - 支持邮件和系统消息
   - 基于 monitor 模块的事件模板系统

2. **重试机制**
   - 为定时任务增加自动重试功能
   - 最大重试次数：5次
   - 失败后发送告警通知

3. **回调监控任务** (`miele_autotask_timer_checksapcallbacks`)
   - 监控 SAP 系统回调状态
   - 超时检测：20分钟
   - 自动发送超时告警

## 数据库变更

### 新增字段

```sql
-- sap_so 表
ALTER TABLE `sdb_miele_sap_so`
ADD COLUMN `retry_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重试次数，用于定时任务重试机制';

-- sap_odn 表
ALTER TABLE `sdb_miele_sap_odn`
ADD COLUMN `retry_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重试次数，用于定时任务重试机制';

-- reship_wo 表
ALTER TABLE `sdb_miele_reship_wo`
ADD COLUMN `retry_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重试次数，用于定时任务重试机制';
```

### 索引优化

```sql
-- 为重试计数字段添加索引
ALTER TABLE `sdb_miele_sap_so` ADD INDEX `ind_retry_count` (`retry_count`);
ALTER TABLE `sdb_miele_sap_odn` ADD INDEX `ind_retry_count` (`retry_count`);
ALTER TABLE `sdb_miele_reship_wo` ADD INDEX `idx_retry_count` (`retry_count`);
```

### 数据库Schema配置

按照 `docs/cheatsheet/database/schema-config.md` 规范，重试字段在各表的schema配置如下：

```php
// sap_so.php 中的配置
'retry_count' => array(
    'type' => 'int unsigned',
    'required' => false,
    'default' => 0,
    'label' => '重试次数',
    'comment' => '定时任务重试次数，用于重试机制控制',
    'in_list' => true,
    'default_in_list' => false,
    'filtertype' => 'normal',
    'filterdefault' => false,
    'order' => 44.5,
),

// sap_odn.php 中的配置
'retry_count' => array(
    'type' => 'int unsigned',
    'required' => false,
    'default' => 0,
    'label' => '重试次数',
    'comment' => '定时任务重试次数，用于重试机制控制',
    'editable' => false,
    'width' => 80,
    'in_list' => true,
    'default_in_list' => false,
    'filtertype' => 'normal',
    'filterdefault' => false,
),

// reship_wo.php 中的配置
'retry_count' => array(
    'type' => 'int unsigned',
    'required' => false,
    'default' => 0,
    'label' => '重试次数',
    'comment' => '定时任务重试次数，用于重试机制控制',
    'editable' => false,
    'in_list' => true,
    'default_in_list' => false,
    'order' => 321.5,
    'filtertype' => 'normal',
    'filterdefault' => false,
),
```

## 通知服务使用指南

### 基本用法

```php
// 获取通知服务实例
$notificationService = kernel::single('miele_service_notification');

// 发送任务重试失败通知
$result = $notificationService->sendTaskRetryFailedNotification(
    '任务名称',
    '关键数据（如订单号）',
    '失败原因',
    5 // 重试次数
);

// 发送SAP回调超时通知
$result = $notificationService->sendSapCallbackTimeoutNotification(
    'SO', // 单据类型
    'SO-123456', // 单据号
    '25分钟', // 超时时长
    '2025-08-04 10:30:00' // 请求时间
);
```

### 通用通知发送

```php
// 使用模板发送通知
$templateVars = [
    'task_name' => '测试任务',
    'key_data' => 'TEST-001',
    'error_message' => '网络超时',
    'retry_count' => 3
];

$result = $notificationService->sendNotification(
    'miele_task_retry_failed', // 模板编码
    $templateVars
);
```

## 重试机制实现

### 定时任务修改模式

所有支持重试的定时任务都遵循以下模式：

1. **数据获取条件**
```php
// 获取条件：状态为pending，或者状态为fail且重试次数小于5
$filter = [
    'filter_sql' => "(status = 'pending' OR (status = 'fail' AND retry_count < 5))"
];
```

2. **异常处理**
```php
try {
    // 执行业务逻辑
    $result = $service->process($data);
    if ($result['res'] == 'succ') {
        // 成功时重置重试次数
        $model->update(['retry_count' => 0], ['id' => $data['id']]);
    } else {
        throw new Exception($result['message']);
    }
} catch (Exception $e) {
    $this->handleRetryLogic($model, $data, $e->getMessage());
}
```

3. **重试逻辑处理**
```php
private function handleRetryLogic($model, $record, $errorMessage) {
    $retryCount = intval($record['retry_count']) + 1;
    
    if ($retryCount >= 5) {
        // 发送失败通知
        $notificationService = kernel::single('miele_service_notification');
        $notificationService->sendTaskRetryFailedNotification(
            '任务名称',
            $record['key_field'],
            $errorMessage,
            $retryCount
        );
        
        // 标记为最终失败
        $model->update([
            'status' => 'fail',
            'retry_count' => $retryCount,
            'message' => '重试次数超限：' . $errorMessage
        ], ['id' => $record['id']]);
    } else {
        // 增加重试次数，等待下次重试
        $model->update([
            'status' => 'fail',
            'retry_count' => $retryCount,
            'message' => '第' . $retryCount . '次重试失败：' . $errorMessage
        ], ['id' => $record['id']]);
    }
}
```

## 回调监控配置

### 定时任务配置

```bash
# 每分钟执行一次
* * * * * cd /path/to/miele && /usr/bin/php index.php autotask timer checksapcallbacks
```

### 监控参数

- **回调超时时间**: 20分钟
- **检查频率**: 每分钟
- **任务锁超时**: 300秒

### 监控逻辑

1. **SO单回调检查**
   - 查询条件：`sap_sync_status = 'running'` 且 `sap_sync_time < 20分钟前` 且 `sap_sync_callback_time IS NULL`
   - 超时处理：发送告警通知

2. **ODN单回调检查**
   - 查询条件：`sap_add_status = 'running'` 且 `up_time < 20分钟前` 且 `sap_odn_bn IS NULL`
   - 超时处理：发送告警通知

## 通知模板

### 任务重试失败通知模板

```
模板编码: miele_task_retry_failed
模板名称: 定时任务重试失败通知
内容模板:
**定时任务重试失败通知**
>任务名称：{task_name}
>关键数据：{key_data}
>失败原因：{error_message}
>重试次数：{retry_count}
>请及时处理相关问题
```

### SAP回调超时通知模板

```
模板编码: miele_sap_callback_timeout
模板名称: SAP回调超时通知
内容模板:
**SAP回调超时通知**
>单据类型：{doc_type}
>单据号：{doc_number}
>超时时长：{timeout_duration}
>请求时间：{request_time}
>请检查SAP系统状态
```

## 维护指南

### 日志监控

重要日志位置和关键词：
- 重试失败：`最终失败`
- 回调超时：`回调超时告警`
- 通知发送：`通知发送`

### 性能监控

- 监控定时任务执行时间
- 监控数据库查询性能
- 监控通知发送成功率

### 故障排查

1. **重试机制不工作**
   - 检查 retry_count 字段是否存在
   - 检查数据获取条件是否正确
   - 检查异常处理逻辑

2. **回调监控不工作**
   - 检查定时任务是否正常执行
   - 检查数据库查询条件
   - 检查通知服务配置

3. **通知发送失败**
   - 检查邮件服务器配置
   - 检查通知模板是否存在
   - 检查接收者配置

## 扩展指南

### 添加新的重试任务

1. 在数据表中添加 `retry_count` 字段
2. 修改任务的数据获取条件
3. 添加异常处理和重试逻辑
4. 集成通知服务

### 自定义通知模板

1. 在 `sdb_monitor_event_template` 表中添加新模板
2. 在 `miele_service_event_template_get` 中注册事件类型
3. 在通知服务中添加专用方法（可选）

## 版本历史

- **v1.0** (2025-08-04): 初始版本，实现基础重试机制和回调监控
