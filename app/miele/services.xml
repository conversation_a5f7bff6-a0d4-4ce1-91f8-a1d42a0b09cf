<services>
    <service id="operation_log">
        <class>miele_operation_log</class>
    </service>
    <service id="desktop_finder.miele_mdl_sales_material_setmapping">
        <class>miele_finder_sales_material_setmapping</class>
    </service>
    <service id="erpapi.service.order.analysis.prepose">
        <class>miele_service_order_create</class>
    </service>
    <service id="ome.service.order.create.after">
        <class>miele_service_order_create</class>
    </service>
    <service id="ome.service.order.reservation">
        <class>miele_service_order_reservation</class>
    </service>
    <service id="ome.service.reship.create.after">
        <class>miele_service_reship_create</class>
    </service>
    <service id="ome.service.reship.check.before">
        <class>miele_service_reship_check</class>
    </service>
    <service id="ome.service.reship.check.after">
        <class>miele_service_reship_check</class>
    </service>
    <service id="ome.service.refund.apply.refundonly.after">
        <class>miele_service_refund_apply_refundonly</class>
    </service>
    <service id="monitor.service.event.template.get.after">
        <class>miele_service_event_template_get</class>
    </service>
    <service id="erpapi.service.plugins.order.coupon.after">
        <class>miele_service_plugins_order_coupon</class>
    </service>
    <service id="desktop_finder.miele_mdl_reship_wo">
        <class>miele_finder_reship</class>
    </service>
    <service id="desktop_finder.miele_mdl_shop_customer_mapping">
        <class>miele_finder_shop_customer_mapping</class>
    </service>
    <service id="desktop_finder.miele_mdl_sap_so">
        <class>miele_finder_sap_so</class>
    </service>
    <service id="desktop_finder.miele_mdl_sap_so_bucha">
        <class>miele_finder_bucha_order</class>
    </service>
    <service id="openapi.conf">
        <class>miele_openapi_conf</class>
    </service>
    <service id="desktop_finder.miele_mdl_sap_goods">
        <class>miele_finder_sap_goods</class>
    </service>
    <service id="desktop_finder.miele_mdl_stock_masterdata">
        <class>miele_finder_stock_masterdata</class>
    </service>
    <service id="desktop_finder.miele_mdl_stock">
        <class>miele_finder_stock</class>
    </service>
    <service id="desktop_finder.miele_mdl_sap_odn">
        <class>miele_finder_sap_odn</class>
    </service>
    <service id="ome.service.order.change.after">
        <class>miele_service_order_change</class>
    </service>

    <!-- 订单更新开始 -->
    <service id="ome.service.refund.apply.objectspaystatus.after">
        <class>miele_service_order_update</class>
    </service>
    <service id="ome.service.reship.finish.after">
        <class>miele_service_order_update</class>
    </service>
    <service id="ome.service.order.finishedit.after">
        <class>miele_service_order_update</class>
    </service>
    <!-- 订单更新结束 -->

    <!-- Miele APP WMS 发货单参数格式化 Service -->
    <service id="erpapi.service.wms.delivery.params.format">
        <class>miele_service_erpapi_wms_delivery</class>
    </service>
</services>