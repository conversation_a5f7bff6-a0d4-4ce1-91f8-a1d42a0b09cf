INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('miele_jiao_jie_bill', '[大家电]物流交接单', 'miele_jiao_jie_bill', 'email', '[大家电]物流交接单
    >业务：<font color=\"warning\">{title}</font>
    >单据：<font color=\"warning\">{bill_bn}</font>
    >接口名：<font color=\"warning\">{method}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2025-08-13 00:00:00', '2025-08-13 00:00:00');

INSERT INTO `sdb_monitor_event_template` 
(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`) 
VALUES 
(
    'miele_major_appliance_address_change', 
    '大家电地址更改重新预约提醒', 
    'miele_major_appliance_address_change', 
    'email', 
    '**大家电地址变更通知**\n> 订单号：{order_bn}\n> 商品名称：{product_name}\n> 原地址：{old_address}\n> 新地址：{new_address}\n> 预约时间：{appointment_time}\n> 客服电话：{service_phone}', 
    1, 
    'system', 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`,
                                         `source`, `disabled`, `at_time`, `up_time`)
VALUES ('miele_order_refund_force_take', '强制退款通知物流上门取件', 'miele_order_refund_force_take', 'email', 
'**强制退款通知物流上门取件**
>订单号：{order_bn}
>退款金额：{refund_fee}
>取件地址：{address}
>物流公司：{logistics_company}
>预计取件时间：{take_time}', '1', 'system', 'false', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`,
                                         `source`, `disabled`, `at_time`, `up_time`)
VALUES ('miele_sap_reservation_fail', 'SAP预约失败OMS消息提醒', 'miele_sap_reservation_fail', 'email',
'**SAP预约失败OMS消息提醒**
>订单号：{order_bn}
>失败原因：{reason}
>预约时间：{reservation_time}', '1', 'system', 'false', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 任务重试失败通知模板
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`,
                                         `source`, `disabled`, `at_time`, `up_time`)
VALUES ('miele_task_retry_failed', 'SAP定时任务重试失败通知', 'miele_task_retry_failed', 'email',
'**SAP定时任务重试失败通知**
>任务名称：{task_name}
>关键数据：{key_data}
>失败原因：{error_message}
>重试次数：{retry_count}
>请及时处理相关问题', '1', 'system', 'false', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- SAP回调超时通知模板
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`,
                                         `source`, `disabled`, `at_time`, `up_time`)
VALUES ('miele_sap_callback_timeout', 'SAP回调超时通知', 'miele_sap_callback_timeout', 'email',
'**SAP回调超时通知**
>单据类型：{doc_type}
>单据号：{doc_number}
>超时时长：{timeout_duration}
>请求时间：{request_time}
>请检查SAP系统状态', '1', 'system', 'false', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);