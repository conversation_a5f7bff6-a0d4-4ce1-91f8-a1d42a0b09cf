<?php

/**
 * Miele 统一通知服务
 * 基于 monitor 模块的事件通知系统，提供标准化的通知发送接口
 * <AUTHOR> Assistant
 * @version 1.0
 */
class miele_service_notification
{
    /**
     * 发送通知消息
     * @param string $templateId 模板ID或模板编码
     * @param array $templateVars 模板变量
     * @param array $receivers 接收者列表（可选，如果不提供则使用模板配置的接收者）
     * @param bool $isSync 是否同步发送，默认为true
     * @return array 发送结果
     */
    public function sendNotification($templateId, $templateVars = [], $receivers = [], $isSync = true)
    {
        try {
            // 获取模板信息
            $templateInfo = $this->getTemplateInfo($templateId);
            if (!$templateInfo) {
                return [
                    'success' => false,
                    'message' => '通知模板不存在: ' . $templateId,
                    'notify_id' => null
                ];
            }

            // 准备通知参数
            $notifyParams = $this->prepareNotifyParams($templateVars);
            
            // 调用 monitor 模块的通知服务
            $eventNotify = kernel::single('monitor_event_notify');
            $result = $eventNotify->addNotify($templateInfo['event_type'], $notifyParams, $isSync);
            
            if ($result) {
                return [
                    'success' => true,
                    'message' => '通知发送成功',
                    'notify_id' => null // monitor 模块的 addNotify 方法不返回 notify_id
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '通知发送失败',
                    'notify_id' => null
                ];
            }
            
        } catch (Exception $e) {
            kernel::log('通知发送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '通知发送异常: ' . $e->getMessage(),
                'notify_id' => null
            ];
        }
    }

    /**
     * 发送任务重试失败通知
     * @param string $taskName 任务名称
     * @param string $keyData 关键数据（如订单号）
     * @param string $errorMessage 失败原因
     * @param int $retryCount 重试次数
     * @return array 发送结果
     */
    public function sendTaskRetryFailedNotification($taskName, $keyData, $errorMessage, $retryCount)
    {
        $templateVars = [
            'task_name' => $taskName,
            'key_data' => $keyData,
            'error_message' => $errorMessage,
            'retry_count' => $retryCount
        ];
        
        return $this->sendNotification('miele_task_retry_failed', $templateVars);
    }

    /**
     * 发送SAP回调超时通知
     * @param string $docType 单据类型（SO/ODN）
     * @param string $docNumber 单据号
     * @param string $timeoutDuration 超时时长
     * @param string $requestTime 请求时间
     * @return array 发送结果
     */
    public function sendSapCallbackTimeoutNotification($docType, $docNumber, $timeoutDuration, $requestTime)
    {
        $templateVars = [
            'doc_type' => $docType,
            'doc_number' => $docNumber,
            'timeout_duration' => $timeoutDuration,
            'request_time' => $requestTime
        ];
        
        return $this->sendNotification('miele_sap_callback_timeout', $templateVars);
    }

    /**
     * 获取模板信息
     * @param string $templateId 模板ID或模板编码
     * @return array|null 模板信息
     */
    private function getTemplateInfo($templateId)
    {
        $eventTemplateMdl = app::get('monitor')->model('event_template');
        
        // 先尝试按模板编码查询
        $template = $eventTemplateMdl->db_dump([
            'template_bn' => $templateId,
            'status' => '1',
            'disabled' => 'false'
        ]);
        
        // 如果按编码找不到，再尝试按ID查询
        if (!$template && is_numeric($templateId)) {
            $template = $eventTemplateMdl->db_dump([
                'template_id' => $templateId,
                'status' => '1',
                'disabled' => 'false'
            ]);
        }
        
        return $template;
    }

    /**
     * 准备通知参数
     * @param array $templateVars 模板变量
     * @return array 处理后的参数
     */
    private function prepareNotifyParams($templateVars)
    {
        // 确保所有变量都是字符串类型
        $params = [];
        foreach ($templateVars as $key => $value) {
            if (is_array($value)) {
                $params[$key] = implode(',', $value);
            } else {
                $params[$key] = (string)$value;
            }
        }
        
        return $params;
    }

    /**
     * 直接发送通知（绕过模板系统）
     * @param string $subject 邮件主题
     * @param string $content 邮件内容
     * @param array $receivers 接收者邮箱列表
     * @return array 发送结果
     */
    public function sendDirectNotification($subject, $content, $receivers = [])
    {
        try {
            if (empty($receivers)) {
                return [
                    'success' => false,
                    'message' => '接收者列表不能为空',
                    'notify_id' => null
                ];
            }

            // 使用 console_email 直接发送邮件
            $emailService = kernel::single('console_email');
            $receiverString = implode(';', $receivers);
            
            $result = $emailService->send($receiverString, $subject, $content);
            
            if ($result[0]) {
                return [
                    'success' => true,
                    'message' => '邮件发送成功',
                    'notify_id' => null
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '邮件发送失败: ' . $result[1],
                    'notify_id' => null
                ];
            }
            
        } catch (Exception $e) {
            kernel::log('直接邮件发送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '邮件发送异常: ' . $e->getMessage(),
                'notify_id' => null
            ];
        }
    }

    /**
     * 获取可用的通知模板列表
     * @return array 模板列表
     */
    public function getAvailableTemplates()
    {
        $eventTemplateMdl = app::get('monitor')->model('event_template');
        return $eventTemplateMdl->getList('template_id,template_bn,template_name,event_type', [
            'status' => '1',
            'disabled' => 'false'
        ]);
    }

    /**
     * 检查模板是否存在
     * @param string $templateId 模板ID或模板编码
     * @return bool 是否存在
     */
    public function templateExists($templateId)
    {
        return $this->getTemplateInfo($templateId) !== null;
    }
}
