<?php
/**
 * Miele APP WMS 发货单参数格式化 Service
 * 
 * 用于处理 miele APP 的 delivery_create 参数格式化差异
 * 
 * @category miele
 * @package miele.service.erpapi.wms
 * <AUTHOR>
 * @version $Id: delivery.php
 */
class miele_service_erpapi_wms_delivery
{
    /**
     * 格式化发货单创建参数
     * 
     * @param array $sdf 发货单数据
     * @param array $params 已格式化的参数
     * @param object $channelObj 渠道对象
     * @return array 修改后的参数
     */
    public function format_delivery_create_params($sdf, $params, $channelObj)
    {
        // 为 items 中的每个商品添加 inventoryType 字段
        if (isset($params['items']) && isset($sdf['storage_code'])) {
            $items = json_decode($params['items'], true);
            if (is_array($items) && isset($items['item'])) {
                foreach ($items['item'] as $key => $item) {
                    $items['item'][$key]['inventoryType'] = $sdf['storage_code'];
                }
                
                $params['items'] = json_encode($items);
            }
        }
        
        return $params;
    }
} 