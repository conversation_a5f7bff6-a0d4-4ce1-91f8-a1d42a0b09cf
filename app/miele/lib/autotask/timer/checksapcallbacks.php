<?php

/**
 * SAP回调检查定时任务
 * 每分钟执行一次，监控SAP系统的回调状态，并在超时后发送警报
 * <AUTHOR> Assistant
 * @version 1.0
 */
class miele_autotask_timer_checksapcallbacks
{
    /**
     * 任务锁key
     */
    const TASK_LOCK_KEY = 'miele_checksapcallbacks_lock';
    
    /**
     * 锁超时时间(秒)
     */
    const LOCK_TIMEOUT = 300;
    
    /**
     * 回调超时时间(分钟)
     */
    const CALLBACK_TIMEOUT_MINUTES = 20;

    /**
     * 任务执行入口
     * @param array $params 任务参数
     * @param string $err_msg 错误信息
     * @return bool 执行结果
     */
    public function process($params, &$err_msg)
    {
        try {
            // 检查任务锁
            if (!$this->acquireLock()) {
                $err_msg = 'SAP回调检查任务正在执行中，请稍后再试';
                return true;
            }

            $soTimeoutCount = 0;
            $odnTimeoutCount = 0;

            // 检查SO单回调超时
            $soTimeoutCount = $this->checkSoCallbacks();
            
            // 检查ODN单回调超时
            $odnTimeoutCount = $this->checkOdnCallbacks();

            $err_msg = sprintf('SAP回调检查完成，SO超时：%d个，ODN超时：%d个', 
                $soTimeoutCount, $odnTimeoutCount);

            // 完成释放锁
            $this->releaseLock();

            return true;
        } catch (Exception $e) {
            $err_msg = 'SAP回调检查失败: ' . $e->getMessage();
            kernel::log('SAP回调检查任务异常: ' . $e->getMessage());
            return false;
        } finally {
            // 释放任务锁
            $this->releaseLock();
        }
    }

    /**
     * 检查SO单回调超时
     * @return int 超时数量
     */
    private function checkSoCallbacks()
    {
        $soMdl = app::get('miele')->model('sap_so');
        $timeoutThreshold = date('Y-m-d H:i:s', time() - self::CALLBACK_TIMEOUT_MINUTES * 60);
        
        // 查询已成功请求SAP但长时间未收到回调的SO单
        $timeoutSoList = $soMdl->getList('id,order_bn,sap_sync_time', [
            'sap_sync_status' => 'running',
            'sap_sync_time|lthan' => $timeoutThreshold,
            'sap_sync_callback_time' => null
        ], 0, 100);

        $timeoutCount = 0;
        if (!empty($timeoutSoList)) {
            $notificationService = kernel::single('miele_service_notification');
            
            foreach ($timeoutSoList as $so) {
                $timeoutDuration = $this->calculateTimeoutDuration($so['sap_sync_time']);
                
                // 发送超时告警
                $result = $notificationService->sendSapCallbackTimeoutNotification(
                    'SO',
                    $so['order_bn'],
                    $timeoutDuration,
                    $so['sap_sync_time']
                );
                
                if ($result['success']) {
                    $timeoutCount++;
                    kernel::log('SO单回调超时告警已发送，订单号: ' . $so['order_bn'] . '，超时时长: ' . $timeoutDuration);
                } else {
                    kernel::log('SO单回调超时告警发送失败，订单号: ' . $so['order_bn'] . '，错误: ' . $result['message']);
                }
            }
        }

        return $timeoutCount;
    }

    /**
     * 检查ODN单回调超时
     * @return int 超时数量
     */
    private function checkOdnCallbacks()
    {
        $odnMdl = app::get('miele')->model('sap_odn');
        $timeoutThreshold = date('Y-m-d H:i:s', time() - self::CALLBACK_TIMEOUT_MINUTES * 60);
        
        // 查询已成功创建发货单但未返回ODN单号的记录
        $timeoutOdnList = $odnMdl->getList('id,delivery_bn,up_time', [
            'sap_add_status' => 'running',
            'up_time|lthan' => $timeoutThreshold,
            'sap_odn_bn' => null
        ], 0, 100);

        $timeoutCount = 0;
        if (!empty($timeoutOdnList)) {
            $notificationService = kernel::single('miele_service_notification');
            
            foreach ($timeoutOdnList as $odn) {
                $timeoutDuration = $this->calculateTimeoutDuration($odn['up_time']);
                
                // 发送超时告警
                $result = $notificationService->sendSapCallbackTimeoutNotification(
                    'ODN',
                    $odn['delivery_bn'],
                    $timeoutDuration,
                    $odn['up_time']
                );
                
                if ($result['success']) {
                    $timeoutCount++;
                    kernel::log('ODN单回调超时告警已发送，发货单号: ' . $odn['delivery_bn'] . '，超时时长: ' . $timeoutDuration);
                } else {
                    kernel::log('ODN单回调超时告警发送失败，发货单号: ' . $odn['delivery_bn'] . '，错误: ' . $result['message']);
                }
            }
        }

        return $timeoutCount;
    }

    /**
     * 计算超时时长
     * @param string $startTime 开始时间
     * @return string 超时时长描述
     */
    private function calculateTimeoutDuration($startTime)
    {
        $start = strtotime($startTime);
        $now = time();
        $duration = $now - $start;
        
        $minutes = floor($duration / 60);
        $hours = floor($minutes / 60);
        
        if ($hours > 0) {
            return $hours . '小时' . ($minutes % 60) . '分钟';
        } else {
            return $minutes . '分钟';
        }
    }

    /**
     * 获取任务锁
     * @return bool
     */
    private function acquireLock() {
        $kvstore = base_kvstore::instance('miele');
        
        // 检查锁是否存在
        $lockValue = null;
        if ($kvstore->fetch(self::TASK_LOCK_KEY, $lockValue)) {
            // 锁存在，检查是否过期
            if ($lockValue > (time() - self::LOCK_TIMEOUT)) {
                return false;
            }
        }
        
        // 设置锁
        return $kvstore->store(
            self::TASK_LOCK_KEY,
            time(),
            self::LOCK_TIMEOUT
        );
    }

    /**
     * 释放任务锁
     */
    private function releaseLock() {
        base_kvstore::instance('miele')->delete(self::TASK_LOCK_KEY);
    }
}
