<?php
/**
 * 补差单推送定时任务
 * 参考 app/miele/lib/autotask/timer/pushso.php 实现
 * <AUTHOR>
 * @version 1.0
 */
class miele_autotask_timer_pushbucha {
    
    /**
     * 任务锁key
     */
    const TASK_LOCK_KEY = 'miele_pushbucha_lock';
    
    /**
     * 锁超时时间(秒)
     */
    const LOCK_TIMEOUT = 300;

    /**
     * 任务执行入口
     * @param array $params 任务参数
     * @param string $err_msg 错误信息
     * @return bool 执行结果
     */
    public function process($params, &$err_msg) {
        try {
            // 检查任务锁
            if (!$this->acquireLock()) {
                $err_msg = '补差单推送任务正在执行中，请稍后再试';
                return true;
            }

            $soMdl = app::get('miele')->model('sap_so');

            // 获取待推送的补差单
            // 获取条件：状态为pending，或者状态为fail且重试次数小于5
            $buchaList = $soMdl->getList('id,order_bn,retry_count', [
                'order_type' => 'BUCHA',
                'filter_sql' => "(sap_sync_status = 'pending' OR (sap_sync_status = 'fail' AND retry_count < 5))"
            ], 0, 100);

            if (!empty($buchaList)) {
                $syncDebitnote = kernel::single('miele_esb_syncdebitnote');
                $successCount = 0;
                $failedCount = 0;

                foreach ($buchaList as $so) {
                    try {
                        $result = $syncDebitnote->pushSo($so['id']);
                        if ($result && isset($result['res']) && $result['res'] == 'succ') {
                            $successCount++;
                            // 成功时重置重试次数
                            $soMdl->update(['retry_count' => 0], ['id' => $so['id']]);
                        } else {
                            throw new Exception($result['message'] ?? '推送失败');
                        }
                    } catch (Exception $e) {
                        $failedCount++;
                        $this->handleRetryLogic($soMdl, $so, $e->getMessage());
                    }
                }

                $err_msg = sprintf('补差单推送完成，总数：%d，成功：%d，失败：%d',
                    count($buchaList), $successCount, $failedCount);
            } else {
                $err_msg = '没有需要推送的补差单';
            }

            // 完成释放锁
            $this->releaseLock();

            return true;
        } catch (Exception $e) {
            $err_msg = '补差单推送失败: ' . $e->getMessage();
            kernel::log('补差单推送任务异常: ' . $e->getMessage());
            return false;
        } finally {
            // 释放任务锁
            $this->releaseLock();
        }
    }
    
    /**
     * 获取任务锁
     * @return bool
     */
    private function acquireLock() {
        $kvstore = base_kvstore::instance('miele');
        
        // 检查锁是否存在
        $lockValue = null;
        if ($kvstore->fetch(self::TASK_LOCK_KEY, $lockValue)) {
            // 锁存在，检查是否过期
            if ($lockValue > (time() - self::LOCK_TIMEOUT)) {
                return false;
            }
        }
        
        // 设置锁
        return $kvstore->store(
            self::TASK_LOCK_KEY,
            time(),
            self::LOCK_TIMEOUT
        );
    }

    /**
     * 释放任务锁
     */
    private function releaseLock() {
        base_kvstore::instance('miele')->delete(self::TASK_LOCK_KEY);
    }

    /**
     * 处理重试逻辑
     * @param object $model 数据模型
     * @param array $record 记录数据
     * @param string $errorMessage 错误信息
     */
    private function handleRetryLogic($model, $record, $errorMessage) {
        $retryCount = intval($record['retry_count']) + 1;

        if ($retryCount >= 5) {
            // 超过最大重试次数，发送失败通知
            $notificationService = kernel::single('miele_service_notification');
            $notificationService->sendTaskRetryFailedNotification(
                '补差单推送任务',
                $record['order_bn'] ?? $record['id'],
                $errorMessage,
                $retryCount
            );

            // 更新状态为最终失败，不再重试
            $model->update([
                'sap_sync_status' => 'fail',
                'retry_count' => $retryCount,
                'sap_sync_msg' => '重试次数超限：' . $errorMessage
            ], ['id' => $record['id']]);

            kernel::log('补差单推送最终失败，ID: ' . $record['id'] . '，错误: ' . $errorMessage);
        } else {
            // 增加重试次数，状态设为fail等待下次重试
            $model->update([
                'sap_sync_status' => 'fail',
                'retry_count' => $retryCount,
                'sap_sync_msg' => '第' . $retryCount . '次重试失败：' . $errorMessage
            ], ['id' => $record['id']]);

            kernel::log('补差单推送失败，将重试，ID: ' . $record['id'] . '，重试次数: ' . $retryCount . '，错误: ' . $errorMessage);
        }
    }
}
