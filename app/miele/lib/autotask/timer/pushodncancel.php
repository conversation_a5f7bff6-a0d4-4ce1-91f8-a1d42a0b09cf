<?php
/**
 * ============================
 * @Author:   AI
 * @describe: ODN取消推送定时任务
 * ============================
 */
class miele_autotask_timer_pushodncancel {

    const TASK_LOCK_KEY = 'miele_pushodncancel_lock';
    const LOCK_TIMEOUT = 300;

    public function process($params, &$err_msg) {
        try {
            if (!$this->acquireLock()) {
                $err_msg = 'ODN取消推送任务正在执行中，请稍后再试';
                return true;
            }

            $odnMdl = app::get('miele')->model('sap_odn');

            // 获取条件：状态为pending，或者状态为fail且重试次数小于5
            $odnList = $odnMdl->getList('id,delivery_bn,retry_count,cancel_status', [
                'filter_sql' => "(cancel_status = 'pending' OR (cancel_status = 'fail' AND retry_count < 5))"
            ], 0, 100);

            if (!empty($odnList)) {
                $odnEsb = kernel::single('miele_esb_syncodn');
                $successCount = 0;
                $failedCount = 0;

                foreach ($odnList as $odn) {
                    try {
                        $result = $odnEsb->cancelOdn($odn['id']);
                        if ($result && isset($result['res']) && $result['res'] == 'succ') {
                            $successCount++;
                            // 成功时重置重试次数
                            $odnMdl->update(['retry_count' => 0], ['id' => $odn['id']]);
                        } else {
                            throw new Exception($result['message'] ?? '取消失败');
                        }
                    } catch (Exception $e) {
                        $failedCount++;
                        $this->handleRetryLogic($odnMdl, $odn, $e->getMessage());
                    }
                }

                $err_msg = sprintf('ODN取消推送完成，总数：%d，成功：%d，失败：%d',
                    count($odnList), $successCount, $failedCount);
            } else {
                $err_msg = '没有需要取消推送的ODN单';
            }

            // 完成释放锁
            $this->releaseLock();

            return true;
        } catch (Exception $e) {
            $err_msg = 'ODN取消推送失败: ' . $e->getMessage();
            return false;
        } finally {
            $this->releaseLock();
        }
    }

    private function acquireLock() {
        $kvstore = base_kvstore::instance('miele');
        $lockValue = null;
        if ($kvstore->fetch(self::TASK_LOCK_KEY, $lockValue)) {
            if ($lockValue > (time() - self::LOCK_TIMEOUT)) {
                return false;
            }
        }
        return $kvstore->store(self::TASK_LOCK_KEY, time(), self::LOCK_TIMEOUT);
    }

    private function releaseLock() {
        base_kvstore::instance('miele')->delete(self::TASK_LOCK_KEY);
    }

    /**
     * 处理重试逻辑
     * @param object $model 数据模型
     * @param array $record 记录数据
     * @param string $errorMessage 错误信息
     */
    private function handleRetryLogic($model, $record, $errorMessage) {
        $retryCount = intval($record['retry_count']) + 1;

        if ($retryCount >= 5) {
            // 超过最大重试次数，发送失败通知
            $notificationService = kernel::single('miele_service_notification');
            $notificationService->sendTaskRetryFailedNotification(
                'ODN取消推送任务',
                $record['delivery_bn'] ?? $record['id'],
                $errorMessage,
                $retryCount
            );

            // 更新状态为最终失败，不再重试
            $model->update([
                'cancel_status' => 'fail',
                'retry_count' => $retryCount,
                'cancel_msg' => '重试次数超限：' . $errorMessage
            ], ['id' => $record['id']]);

            kernel::log('ODN取消推送最终失败，ID: ' . $record['id'] . '，错误: ' . $errorMessage);
        } else {
            // 增加重试次数，状态设为fail等待下次重试
            $model->update([
                'cancel_status' => 'fail',
                'retry_count' => $retryCount,
                'cancel_msg' => '第' . $retryCount . '次重试失败：' . $errorMessage
            ], ['id' => $record['id']]);

            kernel::log('ODN取消推送失败，将重试，ID: ' . $record['id'] . '，重试次数: ' . $retryCount . '，错误: ' . $errorMessage);
        }
    }
}
