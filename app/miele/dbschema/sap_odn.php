<?php
$db['sap_odn'] = array(
    'columns' => array(
        // 必需字段
        'id' => array(
            'type' => 'int unsigned',
            'required' => true,
            'pkey' => true,
            'extra' => 'auto_increment',
            'editable' => false,
            'comment' => '自增主键ID'
        ),
        'at_time' => array(
            'type' => 'TIMESTAMP',
            'default' => 'CURRENT_TIMESTAMP',
            'label' => '创建时间',
            'editable' => false,
            'in_list' => true,
            'order' => 330,
        ),
        'up_time' => array(
            'type' => 'TIMESTAMP',
            'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'label' => '更新时间',
            'editable' => false,
            'in_list' => true,
            'order' => 340,
        ),
        
        // 关联字段
        'delivery_id' => array(
            'type' => 'table:delivery@ome',
            'required' => true,
            'label' => '发货单',
            'comment' => '发货单ID,关联ome_delivery.delivery_id',
            'editable' => false,
            'width' => 140,
            'in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        'delivery_bn' => array(
            'type' => 'varchar(32)',
            'required' => true,
            'label' => '发货单号',
            'comment' => '发货单号',
            'editable' => false,
            'width' => 140,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => true,
            'is_title' => true,
        ),
        'source' => array(
            'type' => array(
                'local' => '本地',
                'sap' => 'SAP',
            ),
            'label' => '单据来源',
            'default' => 'local',
            'editable' => false,
            'width' => 80,
            'in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        
        // 订单信息
        'order_id' => array(
            'type' => 'table:orders@ome',
            'label' => '订单',
            'comment' => '订单ID,关联ome_orders.order_id',
            'editable' => false,
            'width' => 140,
            'in_list' => true,
            'filtertype' => 'normal',
        ),
        'order_bn' => array(
            'type' => 'varchar(32)',
            'label' => '订单号',
            'comment' => '订单号',
            'editable' => false,
            'width' => 140,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
            'filterdefault' => true,
            'in_list' => true,
        ),
        
        // 店铺信息
        'shop_id' => array(
            'type' => 'varchar(32)',
            'label' => '店铺ID',
            'comment' => '店铺ID',
            'editable' => false,
            'in_list' => false,
        ),
        'shop_bn' => array(
            'type' => 'varchar(20)',
            'label' => '店铺编码',
            'comment' => '店铺编码',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
            'filtertype' => 'normal',
        ),
        'shop_type' => array(
            'type' => 'varchar(50)',
            'label' => '店铺类型',
            'comment' => '店铺类型',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
        ),
        
        // 仓库信息
        'branch_id' => array(
            'type' => 'table:branch@ome',
            'label' => '仓库',
            'comment' => '仓库ID,关联ome_branch.branch_id',
            'editable' => false,
            'width' => 120,
            'in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        'branch_bn' => array(
            'type' => 'varchar(32)',
            'label' => '仓库编码',
            'comment' => '仓库编码',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
            'filtertype' => 'normal',
        ),
        
        // 物流信息
        'logi_id' => array(
            'type' => 'table:dly_corp@ome',
            'label' => '承运商',
            'comment' => '物流公司ID,关联ome_dly_corp.corp_id',
            'editable' => false,
            'width' => 120,
            'in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        'logi_code' => array(
            'type' => 'varchar(20)',
            'label' => '承运商编码',
            'comment' => '承运商编码',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
        ),
        'logi_name' => array(
            'type' => 'varchar(200)',
            'label' => '承运商名称',
            'comment' => '承运商名称',
            'editable' => false,
            'width' => 120,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'logi_no' => array(
            'type' => 'varchar(50)',
            'label' => '运单号',
            'comment' => '运单号',
            'editable' => false,
            'width' => 110,
            'in_list' => true,
            'default_in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
            'searchtype' => 'nequal',
        ),
        
        // 发货单状态
        'status' => array(
            'type' => array(
                'succ' => '已发货',
                'failed' => '发货失败',
                'cancel' => '已取消',
                'progress' => '等待配货',
                'timeout' => '超时',
                'ready' => '待处理',
                'stop' => '暂停',
                'back' => '打回',
                'return_back' => '退回',
            ),
            'label' => '发货单状态',
            'comment' => '发货单状态',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
            'default_in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        
        // SAP相关字段
        'sap_odn_bn' => array(
            'type' => 'varchar(32)',
            'label' => 'SAP ODN号',
            'comment' => 'SAP ODN号',
            'editable' => false,
            'width' => 140,
            'in_list' => true,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
        ),
        'sap_add_status' => array(
            'type' => array(
                'none' => '-',
                'pending' => '待同步',
                'running' => '同步中',
                'succ' => '同步成功',
                'fail' => '同步失败',
            ),
            'label' => 'SAP创建状态',
            'comment' => 'SAP创建状态',
            'default' => 'none',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
            'default_in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        'sap_add_msg' => array(
            'type' => 'longtext',
            'label' => 'SAP创建信息',
            'comment' => 'SAP创建信息',
            'editable' => false,
            'in_list' => false,
        ),
        'sap_cancel_status' => array(
            'type' => array(
                'none' => '-',
                'pending' => '待同步',
                'running' => '同步中',
                'succ' => '同步成功',
                'fail' => '同步失败',
            ),
            'label' => 'SAP取消状态',
            'comment' => 'SAP取消状态',
            'default' => 'none',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
            'filtertype' => 'normal',
        ),
        'sap_cancel_msg' => array(
            'type' => 'longtext',
            'label' => 'SAP取消信息',
            'comment' => 'SAP取消信息',
            'editable' => false,
            'in_list' => false,
        ),
        'sap_consign_status' => array(
            'type' => array(
                'none' => '-',
                'pending' => '待同步',
                'running' => '同步中',
                'succ' => '同步成功',
                'fail' => '同步失败',
            ),
            'label' => 'SAP发货状态',
            'comment' => 'SAP发货状态',
            'default' => 'none',
            'editable' => false,
            'width' => 100,
            'in_list' => true,
            'filtertype' => 'normal',
        ),
        'sap_consign_msg' => array(
            'type' => 'longtext',
            'label' => 'SAP发货信息',
            'comment' => 'SAP发货信息',
            'editable' => false,
            'in_list' => false,
        ),
        'retry_count' => array(
            'type' => 'int unsigned',
            'required' => false,
            'default' => 0,
            'label' => '重试次数',
            'comment' => '定时任务重试次数，用于重试机制控制',
            'editable' => false,
            'width' => 80,
            'in_list' => true,
            'default_in_list' => false,
            'filtertype' => 'normal',
            'filterdefault' => false,
        ),
    ),
    'index' => array(
        'ind_delivery_id' => array('columns' => array('delivery_id')),
        'ind_delivery_bn' => array('columns' => array('delivery_bn')),
        'ind_order_bn' => array('columns' => array('order_bn')),
        'ind_sap_add_status' => array('columns' => array('sap_add_status')),
        'ind_sap_cancel_status' => array('columns' => array('sap_cancel_status')),
        'ind_sap_consign_status' => array('columns' => array('sap_consign_status')),
        'ind_at_time' => array('columns' => array('at_time')),
        'ind_retry_count' => array('columns' => array('retry_count')),
    ),
    'comment' => 'SAP ODN单',
    'charset' => 'utf8mb4',
    'engine' => 'innodb',
);
