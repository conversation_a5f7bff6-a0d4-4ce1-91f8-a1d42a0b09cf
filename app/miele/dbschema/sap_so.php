<?php
$db['sap_so'] = array(
    'columns' => array(
        'id' => array(
            'type' => 'number',
            'required' => true,
            'pkey' => true,
            'extra' => 'auto_increment',
            'label' => '自增主键',
            'editable' => false,
        ),
        'at_time' => array(
            'type' => 'TIMESTAMP',
            'label' => '创建时间',
            'default' => 'CURRENT_TIMESTAMP',
            'filtertype' => 'time',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => true,
            'order' => 98,
        ),
        'up_time' => array(
            'type' => 'TIMESTAMP',
            'label' => '更新时间',
            'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 99,
        ),
        'order_id' => array(
            'type' => 'int unsigned',
            'required' => false,
            'label' => '订单ID',
            'in_list' => true,
            'order' => 10,
        ),
        'order_bn' => array(
            'type' => 'varchar(32)',
            'required' => false,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
            'filterdefault' => true,
            'label' => '订单号',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 11,
        ),
        'order_type' => array(
            'type' => array(
                'NORMAL' => '普通订单',
                'LOCAL' => '手工订单',
                'JDECARD' => '京东E卡',
                'BUCHA' => '补差订单',
                'TMYPDQ' => '天猫优品',
                'JDGUOBU' => '京东超链国补',
                'ADJ-O' => '运费补差',
            ),
            'required' => true,
            'label' => '订单类型',
            'default' => 'NORMAL',
            'comment' => 'NORMAL（普通），LOCAL（手工），JDECARD（京东E卡），BUCHA（补差），TMYPDQ（天猫优品），JDGUOBU（京东超链国补）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 12,
        ),
        'order_status' => array(
            'type' => array(
                'active' => '正常',
                'dead' => '取消',
                'finish' => '完成',
            ),
            'required' => false,
            'label' => '订单状态',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 13,
        ),
        'bill_label' => array(
            'type' => 'varchar(32)',
            'required' => false,
            'label' => '订单分类',
            'comment' => 'LARGE_APPLIANCES（专车类订单），SMALL_APPLIANCES（快递类订单）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 14,
        ),
        'platform_order_bn' => array(
            'type' => 'varchar(32)',
            'required' => false,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
            'filterdefault' => true,
            'label' => '平台订单号',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 15,
        ),
        'related_order_bn' => array(
            'type' => 'varchar(32)',
            'required' => false,
            'label' => '关联订单号',
            'comment' => '补差订单替换使用，默认为空',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 15,
        ),
        'createtime' => array(
            'type' => 'time',
            'required' => false,
            'label' => '下单时间',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 16,
        ),
        'paytime' => array(
            'type' => 'time',
            'required' => false,
            'label' => '支付时间',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 17,
        ),
        'is_invoice' => array(
            'type' => array(
                '0' => '否',
                '1' => '是',
            ),
            'required' => false,
            'label' => '是否开票',
            'comment' => '0（否），1（是）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 18,
        ),
        'invoice_kind' => array(
            'type' => array(
                '0' => '纸票',
                '1' => '电票',
                '2' => '专票',
            ),
            'required' => false,
            'label' => '发票类型',
            'comment' => '0（纸票），1（电票），2（专票）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 18,
        ),
        'invoice_title' => array(
            'type' => 'varchar(255)',
            'required' => false,
            'label' => '发票抬头',
            'in_list' => false,
            'default_in_list' => false,
            'order' => 19,
        ),
        'ship_area' => array(
            'type' => 'varchar(255)',
            'required' => false,
            'label' => '省/市/区',
            'in_list' => false,
            'default_in_list' => false,
            'order' => 20,
        ),
        'ship_addr' => array(
            'type' => 'varchar(255)',
            'required' => false,
            'label' => '联系地址',
            'in_list' => false,
            'default_in_list' => false,
            'order' => 21,
        ),
        'ship_mobile' => array(
            'type' => 'varchar(200)',
            'required' => false,
            'label' => '联系电话',
            'in_list' => false,
            'default_in_list' => false,
            'order' => 22,
        ),
        'ship_name' => array(
            'type' => 'varchar(255)',
            'required' => false,
            'label' => '联系人',
            'in_list' => false,
            'default_in_list' => false,
            'order' => 23,
        ),
        'seller_code' => array(
            'type' => 'varchar(50)',
            'required' => false,
            'label' => '销售员编码',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 24,
        ),
        'order_memo' => array(
            'type' => 'longtext',
            'required' => false,
            'label' => '订单备注',
            'in_list' => false,
            'default_in_list' => false,
            'order' => 25,
        ),
        'delivery_time' => array(
            'type' => 'int',
            'required' => false,
            'label' => '发货时间',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 26,
        ),
        'shop_id' => array(
            'type' => 'varchar(32)',
            'required' => false,
            'label' => '店铺ID',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 27,
        ),
        'shop_bn' => array(
            'type' => 'varchar(20)',
            'required' => false,
            'label' => '店铺编码',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 28,
        ),
        'shop_type' => array(
            'type' => 'varchar(50)',
            'required' => false,
            'label' => '店铺类型',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 29,
        ),
        'logi_id' => array(
            'type' => 'table:dly_corp@ome',
            'required' => false,
            'label' => '承运商ID',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 32,
        ),
        'logi_code' => array(
            'type' => 'varchar(20)',
            'required' => false,
            'label' => '承运商编码',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 33,
        ),
        'logi_name' => array(
            'type' => 'varchar(200)',
            'required' => false,
            'label' => '承运商名称',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 34,
        ),
        'logi_no' => array(
            'type' => 'varchar(50)',
            'required' => false,
            'label' => '运单号',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 35,
        ),
        'total_amount' => array(
            'type' => 'money',
            'required' => false,
            'label' => '订单金额',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 36,
        ),
        'sap_sync_status' => array(
            'type' => array(
                'none' => '未同步',
                'pending' => '待同步',
                'running' => '同步中',
                'succ' => '同步成功',
                'fail' => '同步失败',
            ),
            'required' => false,
            'label' => 'SAP同步状态',
            'comment' => 'none（-），pending（待同步），running（同步中），succ（同步成功），fail（同步失败）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 37,
        ),
        'sap_sync_msg' => array(
            'type' => 'longtext',
            'required' => false,
            'label' => 'SAP同步信息',
            'in_list' => false,
            'default_in_list' => false,
            'order' => 38,
        ),
        'sap_sync_time' => array(
            'type' => 'time',
            'required' => false,
            'label' => 'SAP同步时间',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 39,
        ),
        'sap_sync_callback_time' => array(
            'type' => 'time',
            'required' => false,
            'label' => 'SAP同步回调时间',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 39,
        ),
        'sap_so_bn' => array(
            'type' => 'varchar(32)',
            'required' => false,
            'label' => 'SAP单号',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 39,
        ),
        'custom_reserved' => array(
            'type' => array(
                '0' => '未预约',
                '1' => '已预约',
            ),
            'required' => false,
            'label' => '客人预约状态',
            'comment' => '0（未预约），1（已预约）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 40,
        ),
        'reserved_time' => array(
            'type' => 'time',
            'required' => false,
            'label' => '客人预约时间',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 41,
        ),
        'sap_rsv_status' => array(
            'type' => array(
                '0' => '待预约',
                '1' => '预约成功',
                '2' => '预约失败',
            ),
            'required' => false,
            'label' => 'SAP预约状态',
            'comment' => '0（待预约），1（预约成功），2（预约失败）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 42,
        ),
        'sap_rsv_reason' => array(
            'type' => 'text',
            'required' => false,
            'label' => 'SAP预约原因',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 43,
        ),
        'sap_rsv_time' => array(
            'type' => 'time',
            'required' => false,
            'label' => 'SAP预约时间',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 44,
        ),
        'retry_count' => array(
            'type' => 'int unsigned',
            'required' => false,
            'default' => 0,
            'label' => '重试次数',
            'comment' => '定时任务重试次数，用于重试机制控制',
            'in_list' => true,
            'default_in_list' => false,
            'filtertype' => 'normal',
            'filterdefault' => false,
            'order' => 44.5,
        ),
        'source' => array(
            'type' => array(
                'normal' => '普通订单',
                'order' => '订单补差',
                'reship' => '退货单补差',
            ),
            'required' => true,
            'default' => 'normal',
            'label' => '数据来源',
            'comment' => 'order（订单补差），reship（退货单补差）',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 45,
        ),
    ),
    'index' => array(
        'ind_order_id' => array(
            'columns' => array(
                'order_id',
            ),
            'prefix' => 'unique',
        ),
        'ind_order_bn' => array(
            'columns' => array(
                'order_bn',
            ),
        ),
        'ind_platform_order_bn' => array(
            'columns' => array(
                'platform_order_bn',
            ),
        ),
        'ind_sap_so_bn' => array(
            'columns' => array(
                'sap_so_bn',
            ),
        ),
        'ind_sap_sync_status' => array(
            'columns' => array(
                'sap_sync_status',
            ),
        ),
        'ind_createtime' => array(
            'columns' => array(
                'createtime',
            ),
        ),
        'ind_at_time' => array(
            'columns' => array(
                'at_time',
            ),
        ),
        'ind_up_time' => array(
            'columns' => array(
                'up_time',
            ),
        ),
        'ind_retry_count' => array(
            'columns' => array(
                'retry_count',
            ),
        ),
    ),
    'comment' => 'SAP SO单',
    'engine' => 'innodb',
    'version' => '$Rev: $',
);
