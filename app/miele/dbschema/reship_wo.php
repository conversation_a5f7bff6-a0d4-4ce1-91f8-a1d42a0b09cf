<?php

$db['reship_wo'] = array(
    'columns' => array(
        'id'   => array(
            'type'     => 'int unsigned',
            'extra'    => 'auto_increment',
            'pkey'     => true,
            'editable' => false,
            'label'    => '自增ID',
        ),
        'reship_bn'  => array(
            'type'          => 'varchar(255)',
            'label'         => '退货单号',
            'default'       => '',
            'is_title'      => true,
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 10,
            'searchtype'    => 'nequal',
            'filtertype'    => 'normal',
            'filterdefault' => true,
        ),
        'shop_id'   => array(
            'type'          => 'table:shop@ome',
            'label'         => '店铺',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 20,
            'filtertype'    => 'normal',
            'filterdefault' => true,
        ),
        'shop_bn'   => array(
            'type'          => 'varchar(255)',
            'label'         => '店铺编号',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 30,
        ),
        'return_type'   => array(
            'type'          => [
                'return' => '退货',
                'change' => '换货',
                'refund' => '仅退款',
            ],
            'label'         => '退货类型',
            'default'       => 'return',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 40,
        ),
        'shop_type'   => array(
            'type'          => 'varchar(255)',
            'label'         => '店铺类型',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 50,
        ),
        'reship_id'      => array(
            'type'          => 'table:reship@ome',
            'label'         => '关联退货单ID',
            'in_list'       => false,
            'default_in_list' => false,
            'order'         => 60,
        ),
        'reship_apply_time'   => array(
            'type'          => 'time',
            'label'         => '申请时间',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 70,
        ),
        'reship_complete_time'   => array(
            'type'          => 'time',
            'label'         => '完成时间',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 80,
        ),
        'is_major_appliance'  => array(
            'type'          => [
                '0' => '否',
                '1' => '是',
            ],
            'label'         => '是否为大家电',
            'default'       => '0',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 170,
        ),
        'reship_logi_no'  => array(
            'type'          => 'varchar(255)',
            'label'         => '回寄承运商单号',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 90,
        ),
        'reship_logi_name'  => array(
            'type'          => 'varchar(255)',
            'label'         => '回寄承运商名称',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 100,
        ),
        'reship_reason'   => array(
            'type'          => 'text',
            'label'         => '退货原因',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 110,
        ),
        'reship_status'   => array(
            'type'          => [
                'active' => '处理中',
                'finish' => '完成',
                'refuse' => '拒绝',
            ],
            'label'         => '单据状态',
            'default'       => 'active',
            'in_list'       => true,
            'default_in_list' => true,
        ),
        'refund_fee'     => array(
            'type'          => 'money',
            'label'         => '退款金额',
            'default'       => '0',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 120,
        ),
        'memo'          => array(
            'type'          => 'text',
            'label'         => '备注',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 130,
        ),
        'inbound_status'   => array(
            'type'          => [
                '0' => '未入库',
                '1' => '已入库',
                '2' => '入库中',
                '3' => '拒绝入库',
            ],
            'label'         => '入库状态',
            'default'       => '0',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 140,
        ),
        'order_id'      => array(
            'type'          => 'table:orders@ome',
            'label'         => '关联订单ID',
            'in_list'       => false,
            'default_in_list' => false,
            'order'         => 150,
        ),
        'order_bn'      => array(
            'type'          => 'varchar(255)',
            'label'         => '订单号',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 160,
            'searchtype'    => 'nequal',
            'filtertype'    => 'normal',
            'filterdefault' => true,
        ),
        'platform_order_bn'      => array(
            'type'          => 'varchar(255)',
            'label'         => '平台订单号',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 170,
            'searchtype'    => 'nequal',
            'filtertype'    => 'normal',
            'filterdefault' => true,
        ),
        'qc_status' =>  array(
            'type'          => [
                '0' => '未质检',
                '1' => '质检成功',
                '2' => '质检中',
                '3' => '质检失败',
            ],
            'label'         => '质检状态',
            'default'       => '0',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 180,
        ),
        'is_pickup' => array(
            'type'          => [
                '0' => '否',
                '1' => '是',
            ],
            'label'         => '是否上门质检',
            'default'       => '0',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 190,
        ),
        'pickup_name'  => array(
            'type'          => 'varchar(255)',
            'label'         => '联系人姓名',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 200,
        ),
        'pickup_mobile'  => array(
            'type'          => 'varchar(255)',
            'label'         => '联系人手机',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 210,
        ),
        'pickup_area'  => array(
            'type'          => 'region',
            'label'         => '联系人地区',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 220,
        ),
        'pickup_address'  => array(
            'type'          => 'text',
            'label'         => '联系人地址',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 230,
        ),
        'pickup_logi_no'  => array(
            'type'          => 'varchar(255)',
            'label'         => '运单号',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 240,
        ),
        'pickup_logi_code'  => array(
            'type'          => 'varchar(255)',
            'label'         => '物流公司编码',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 250,
        ),
        'pickup_logi_name'  => array(
            'type'          => 'varchar(255)',
            'label'         => '物流公司名称',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 260,
        ),
        'pickup_logi_paid'  => array(
            'type'          => 'money',
            'label'         => '物流费',
            'default'       => '0',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 270,
        ),
        'sap_sync_status'  => array(
            'type'          => [
                'none' => '-',
                'pending' => '待同步',
                'running' => '同步中',
                'succ' => '同步成功',
                'fail' => '同步失败',
            ],
            'label'         => 'SAP同步状态',
            'default'       => 'none',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 280,
        ),
        'sap_sync_msg'  => array(
            'type'          => 'text',
            'label'         => 'SAP同步信息',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 290,
        ),
        'sap_reship_bn'  => array(
            'type'          => 'varchar(255)',
            'label'         => 'SAP退货单号',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 300,
        ),
        'adj_order_bn'  => array(
            'type'          => 'varchar(255)',
            'label'         => '补差SO单号',
            'default'       => '',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 310,
            'searchtype'    => 'nequal',
            'filtertype'    => 'normal',
            'filterdefault' => true,
        ),
        'is_7toreturn' => array(
            'type' => [
                'yes' => '是',
                'no' => '否',
                'pending' => '待确认'
            ],
            'label' => '7天无理由',
            'default' => 'pending',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 315,
        ),
        'cs_status' => array(
            'type' => [
                '0' => '无需介入',
                '1' => '需要客服介入',
                '2' => '客服已经介入',
                '3' => '客服初审完成',
                '4' => '客服主管复审失败',
                '5' => '客服处理完成',
                '6' => '系统撤销(B2B使用)，维权撤销(集市使用)',
                '7' => '支持买家',
                '8' => '支持卖家',
                '9' => '举证中',
                '10' => '开放申诉',
                '11' => '开放申诉'
            ],
            'label' => '客服介入状态',
            'default' => '0',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 326,
        ),
        'pickup_type' => array(
            'type' => [
                '1' => '上门质检',
                '2' => '消费者自行寄回',
                '3' => '上门取件'
            ],
            'label' => '取件类型',
            'default' => '2',
            'in_list' => true,
            'order' => 316,
        ),
        'logistics_fee_required' => array(
            'type' => [
                '0' => '否',
                '1' => '是',
            ],
            'label' => '需支付物流费用',
            'default' => '0',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 317,
        ),
        'logistics_fee' => array(
            'type' => 'money',
            'label' => '物流费用金额',
            'default' => '0',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 318,
        ),
        'qc_result' => array(
            'type' => [
                'pending' => '待质检',
                'pass' => '通过',
                'fail' => '不通过'
            ],
            'label' => '质检结果',
            'default' => 'pending',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 319,
        ),
        'branch_id'  => array(
            'type'          => 'table:branch@ome',
            'label'         => '仓库',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 320,
        ),
        'sap_sync_time' => array(
            'type' => 'time',
            'label' => 'SAP同步时间',
            'default' => null,
            'in_list' => true,
            'default_in_list' => true,
            'order' => 321,
            'comment' => 'SAP数据同步时间，记录最后一次同步时间',
        ),
        'retry_count' => array(
            'type' => 'int unsigned',
            'required' => false,
            'default' => 0,
            'label' => '重试次数',
            'comment' => '定时任务重试次数，用于重试机制控制',
            'editable' => false,
            'in_list' => true,
            'default_in_list' => false,
            'order' => 321.5,
            'filtertype' => 'normal',
            'filterdefault' => false,
        ),
        'bucha_status' => array(
            'type' => [
                'pending' => '待处理',
                'processing' => '处理中',
                'completed' => '已完成',
                'cancelled' => '已取消'
            ],
            'label' => '补差状态',
            'default' => 'pending',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 322,
            'comment' => '补差订单处理状态',
        ),
        'bill_label'  => array(
            'type'          => 'varchar(255)',
            'label'         => '订单分类',
            'default'       => 'SMALL_APPLIANCES',
            'commit'        => '可选值：LARGE_APPLIANCES（大家电类订单），SMALL_APPLIANCES（小家电类订单）',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 330,
        ),
        'is_adj_order'  => array(
            'type'          => [
                '0' => '否',
                '1' => '是',
            ],
            'label'         => '是否补差订单',
            'default'       => '0',
            'in_list'       => true,
            'default_in_list' => true,
            'order'         => 340,
        ),
        'org_id' =>
        array(
            'type' => 'table:operation_organization@ome',
            'label' => '运营组织',
            'editable' => false,
            'width' => 60,
            'filtertype' => 'normal',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'at_time'       => array(
            'type'            => 'TIMESTAMP',
            'label'           => '创建时间',
            'default'         => 'CURRENT_TIMESTAMP',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 1000,
        ),
        'up_time'       => array(
            'type'            => 'TIMESTAMP',
            'label'           => '更新时间',
            'default'         => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 1010,
        ),
    ),
    'index'   => array(
        'idx_reship_bn'    => array('columns' => array('reship_bn','shop_id','return_type'), 'prefix' => 'unique'),
        'idx_order_bn'     => array('columns' => array('order_bn')),
        'idx_platform_order_bn'     => array('columns' => array('platform_order_bn')),
        'idx_bill_label'     => array('columns' => array('bill_label')),
        'idx_adj_order_bn'     => array('columns' => array('adj_order_bn')),
        'idx_is_7toreturn'     => array('columns' => array('is_7toreturn')),
        'idx_cs_status'     => array('columns' => array('cs_status')),
        'idx_pickup_type'     => array('columns' => array('pickup_type')),
        'idx_qc_result'     => array('columns' => array('qc_result')),
        'idx_sap_sync_time' => array('columns' => array('sap_sync_time')),
        'idx_bucha_status'  => array('columns' => array('bucha_status')),
        'idx_at_time'       => array('columns' => array('at_time')),
        'idx_up_time'       => array('columns' => array('up_time')),
        'idx_retry_count'   => array('columns' => array('retry_count')),
    ),
    'engine'  => 'innodb',
    'commit'  => '',
    'version' => 'Rev: 41996 $',
);