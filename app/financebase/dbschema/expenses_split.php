<?php
$db['expenses_split']=array (
  'columns' => 
  array (
    'id' => 
    array (
      'type' => 'bigint unsigned',
      'required' => true,
      'pkey' => true,
      'extra' => 'auto_increment',
      'editable' => false,
    ),
    'split_bn' => array(
        'type' => 'varchar(50)',
        'label' => '拆分单号',
        'editable' => false,
        'in_list' => true,
        'default_in_list' => true,
        'filtertype' => 'normal',
        'filterdefault' => true,
        'order' => 2,
    ),
    'bm_id' => array(
      'type' => 'table:basic_material@material',
      'label' => '基础物料名称',
      'width' => 120,
      'editable' => false,
      'in_list' => true,
      'default_in_list' => true,
      'order'=>5,
    ),
    'bill_id' => array(
      'type' => 'table:bill@financebase',
      'label' => '业务流水号',
      'width' => 180,
      'in_list' => true,
      'default_in_list' => true,
      'order'=>10,
    ),
    'parent_id' => array(
      'type' => 'bigint unsigned',
      'label' => '来源ID',
      'width' => 130,
      'order'=>15,
    ),
    'parent_type' => array(
      'type' => 'varchar(32)',
      'label' => '来源类型',
      'width' => 130,
      'order'=>20,
    ),
    'trade_time' => array(
        'type' => 'time',
        'label' => '账单时间',
        'width' => 150,
        'filtertype' => 'normal',
        'filterdefault' => true,
        'editable' => false,
        'in_list' => true,
        'default_in_list' => false,
        'order'=>25,
    ),
    'split_time' => array(
        'type' => 'time',
        'label'=>'拆分时间',
        'width' => 150,
        'filtertype' => 'normal',
        'filterdefault' => true,
        'editable' => false,
        'in_list' => true,
        'default_in_list' => false,
        'order'=>25,
    ),
    'money' => array(
          'type' => 'decimal(20,5)',
          'required' => true,
          'label'=>'分摊费用',
          'width' => 100,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => true,
          'order'=>35,
    ),
    'bill_category' => array(
          'type' => 'varchar(50)',
          'label' => '具体类别',
          'width' => 100,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => true,
          'order'=>30,
    ),
    'split_type' => array(
          'type' => 'varchar(32)',
          'label' => '拆分维度',
          'width' => 100,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => false,
          'order'=>40,
    ),
    'split_rule' => array(
          'type' => 'varchar(32)',
          'label' => '拆分规则',
          'width' => 100,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => false,
          'order'=>45,
    ),
    'split_status' => array(
          'type' => array(
            '0'=>'拆分项',
            '1'=>'调整项',
            '2'=>'红冲项',
          ),
          'label' => '拆分状态',
          'width' => 100,
          'default' => '0',
          'editable' => false,
          'in_list' => true,
          'default_in_list' => false,
          'order'=>45,
    ),
    'porth' => array(
          'type' => 'money',
          'label' => '拆分贡献比',
          'default' => 0,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => false,
          'order'=>50,
    ),
    'shop_id' => array(
            'type' => 'varchar(32)',
            'editable' => false,
            'label' => '来源店铺',
            'comment'=>'来源店铺',
            'width' => 100,
            'order'=>55,
    ),
    'is_accounted' => array(
        'type' => array(
            '0' => '未记账',
            '1' => '已记账',
        ),
        'label' => '是否已记账',
        'default' => '0',
        'editable' => false,
        'in_list' => true,
        'default_in_list' => false,
        'order' => 60,
        'comment' => '标记该拆分项是否已记账',
    ),
  ),
  'index' => array(
    'ind_split_bn' => array('columns' => array(0 => 'split_bn'),'prefix' => 'UNIQUE'),
    'ind_trade_time' => array('columns' => array(0 => 'trade_time')),
    'ind_split_time' => array('columns' => array(0 => 'split_time')),
    'ind_split_status' => array('columns' => array(0 => 'split_status')),
    'ind_bill_category' => array('columns' => array(0 => 'bill_category')),
    'ind_is_accounted' => array('columns' => array(0 => 'is_accounted')),
   ),
  'comment' => '费用拆分表',
  'engine' => 'innodb',
  'version' => '$Rev:  $',
);
