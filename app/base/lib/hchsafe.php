<?php
/**
 * 淘宝御城河
 *
 * @category 
 * @package 
 * <AUTHOR>
 * @version $Id: Z
 */
class base_hchsafe
{

    /**
     * 登录日志
     *
     * @return void
     * <AUTHOR>
    public function login_log($params)
    {
        kernel::single('erpapi_router_request')->set('hchsafe','taobao')->hchsafe_login($params);
        
        kernel::single('erpapi_router_request')->set('hchsafe','360buy')->hchsafe_login($params);
        kernel::single('erpapi_router_request')->set('hchsafe','luban')->hchsafe_login($params);
    }

    /**
     * 风控风险分析
     *
     * @return void
     * <AUTHOR>
    public function compute_risk($params,&$msg)
    {
        if (defined('DEV_ENV')) {
            return true;
        }
        
        // IDAAS
        $rs = ['rsp' => 'succ'];
        if (defined('IDAAS_LOGIN') && true == constant("IDAAS_LOGIN")) {
            $rs = kernel::single('erpapi_router_request')->set('idaas', 'aliyun')->account_login(array (
                'login_name'     => $params['uname'],
                'login_password' => $params['password_string'],
                'member_id'      => $params['member_id'],
            ));
        }
        if($rs['rsp'] == 'succ'){
            $rs = kernel::single('erpapi_router_request')->set('hchsafe','360buy')->hchsafe_computerisk();
        }

        if ($rs['rsp'] == 'fail') $msg = $rs['msg'];

        return $rs['rsp'] == 'succ' ? true : false;
    }


    /**
     * 订单访问(打印)
     *
     * @return void
     * <AUTHOR>
    public function order_log($params)
    {
       
        kernel::single('erpapi_router_request')->set('hchsafe','taobao')->hchsafe_orderdata($params);
        kernel::single('erpapi_router_request')->set('hchsafe','360buy')->hchsafe_orderdata($params);
        kernel::single('erpapi_router_request')->set('hchsafe','luban')->hchsafe_orderdata($params);
    }


    /**
     * 订单访问
     *
     * @return void
     * <AUTHOR>
    public function sql_log($params)
    {
        kernel::single('erpapi_router_request')->set('hchsafe','taobao')->hchsafe_sql($params);
    }

    public function order_push_log($params)
    {
        kernel::single('erpapi_router_request')->set('hchsafe','taobao')->hchsafe_orderpush($params);
        kernel::single('erpapi_router_request')->set('hchsafe','360buy')->hchsafe_orderpush($params);
        kernel::single('erpapi_router_request')->set('hchsafe','luban')->hchsafe_orderpush($params);
    }

    public function isVerifyPassed($params) {
        return kernel::single('erpapi_router_request')->set('hchsafe','taobao')->hchsafe_isVerifyPassed($params);
    }
}
