  <!--<div class="borderGray" style="margin:5px 0; padding:5px 10px;border:1px solid #ddd;background:#f3f3f3"><b>当天数据请于第二天查看</b><br/>
    当前时间不足一月，本月数据只显示到当前时间前一天的数据</div>-->
    
<div class="data-action" id="dataAction" style="padding:5px;">
  <table width="100%" cellpadding="0" cellspacing="0" border="0">
    
    <tr>
      <td nowrap="nowrap">
        <label><{t}>搜索条件:<{/t}></label>
       <select name='search_filter'>
       <option value="order_bn" <{if $env.post.search_filter=='order_bn'}>selected<{/if}>>订单号</option>
       <option value="logi_no" <{if $env.post.search_filter=='logi_no'}>selected<{/if}>>物流单号</option>
       <option value="member_name" <{if $env.post.search_filter=='member_name'}>selected<{/if}>>用户名</option>
       <option value="receive_name" <{if $env.post.search_filter=='receive_name'}>selected<{/if}>>收件人</option>
       <option value="mobile" <{if $env.post.search_filter=='mobile'}>selected<{/if}>>手机</option>
       <option value="tel" <{if $env.post.search_filter=='tel'}>selected<{/if}>>电话</option>
       <option value="delivery_bn" <{if $env.post.search_filter=='delivery_bn'}>selected<{/if}>>发货单号</option>
       </select><input type="text" name="search_filter_value" id="search_filter_value" value="<{$env.post.search_filter_value}>">
        <span id="tohidden"></span>
        <input type="hidden" name="flag" value='1'>
        <label><{t}>店铺:<{/t}></label><select name='shop_id'>
      <option value="">请选择</option>
        <{foreach from=$shopList item=shop}>
        <option value="<{$shop.shop_id}>" <{if $env.post.shop_id==$shop.shop_id}>selected<{/if}>><{$shop.name}></option>
        <{/foreach}>
        </select>
      </td>
    </tr>
    <tr>
        <td nowrap="nowrap"> 
      </td>
    </tr>
    <tr>
      <td nowrap="nowrap">
     
        <label><{t}>时间范围:<{/t}></label>
        <{if $report != "month"}>
            <{input type="date" vtype="date" name="time_from" style="width:96px; font-family:arial;" value=$time_from}><{t}>至<{/t}>
            <{input type="date" vtype="date" name="time_to" style="width:96px; font-family:arial;" value=$time_to}>
        <{else}>
        <select id="from_year" class="date_select">
          <{foreach from=$year item=item}>
            <option value="<{$item}>" <{if $from_selected[0] == $item}>selected="selected"<{/if}>><{$item}></option>
          <{/foreach}>
        </select><{t}>年<{/t}>
        <select id="from_month" class="date_select">
          <{foreach from=$month item=item}>
            <option value="<{$item}>" <{if $from_selected[1] == $item}>selected="selected"<{/if}>><{$item}></option>
          <{/foreach}>
        </select><{t}>月 <{/t}><{t}>至<{/t}>
        <select id="to_year" class="date_select">
          <{foreach from=$year item=item}>
            <option value="<{$item}>" <{if $to_selected[0] == $item}>selected="selected"<{/if}>><{$item}></option>
          <{/foreach}>
        </select><{t}>年<{/t}>
        <select id="to_month" class="date_select">
          <{foreach from=$month item=item}>
            <option value="<{$item}>" <{if $to_selected[1] == $item}>selected="selected"<{/if}>><{$item}></option>
          <{/foreach}>
        </select><{t}>月<{/t}>
        <input type="hidden" name="time_from" value="<{$time_from}>">
        <input type="hidden" name="time_to" value="<{$time_to}>">
        <{/if}>
        <span id="tohidden"></span>
        
        <{button class="filterBtn btn-thirdly" label=$___ectools="确定"|t:'ectools'}>
      </td>
    </tr>
  </table>
</div>

<script>
  (function(){
    //if($E('.num')) $E('.num').innerHTML+=' 数据非及时显示，当天数据请于第二天查看';
    var _finder = finderGroup['<{$name}>'],
        dataAction= $('dataAction'),
        data_ipt=$$(dataAction.getElements('input[name^=time_]'),dataAction.getElements('select')),
        from=dataAction.getElement('input[name=time_from]'),
        to=dataAction.getElement('input[name=time_to]');
       
    if(_finder&&_finder.form){
      fdoc = document.createDocumentFragment();
      data_ipt.each(function(ipt){
        fdoc.appendChild(new Element('input[type=hidden]', {'name': ipt.name, value: ipt.value}));
      });
      _finder.form.appendChild(fdoc);
    }

    data_ipt.addEvent('change',function(e){
      if(_finder&&_finder.form){
        var ipt=$E('input[name='+this.name+']',_finder.form);
        if(ipt)ipt.value=this.value;
      }
    });

    <{if $report == "month"}>
    var today=new Date();
    $$('select.date_select').each(function(item){
      /* $A(item.options).each(function(opt,i){
         if(opt.text==today.getFullYear() || opt.text==today.getMonth()+1) opt.selected=true;
      }); */
      item.addEvent('change',function(){
        setDate(this.get('id').split('_')[0]);
      });
    });

    setDate();

    function setDate(){
      var arguments=arguments.length===0 ? ['from','to'] : arguments;
      $A(arguments).each(function(item){
        dataAction.getElement('input[name=time_'+item+']').value=$(item+'_year').options[$(item+'_year').selectedIndex].text+'-'+$(item+'_month').options[$(item+'_month').selectedIndex].text+'-01';
      });
    }
    <{/if}>

    function setChartData(flag,e){

      if(!check_data(from,to))return;
      var params=dataAction.toQueryString(),
          itemcur=$E('.chart-view .chart-item-cur');

          dataAction.store('_data',params);
          if(flag&&itemcur&&$E('iframe',itemcur)) $E('iframe',itemcur).src+='&'+params;
          if(flag) url='index.php?app=<{$env.get.app}>&ctl=<{$env.get.ctl}>&act=<{$env.get.act}>';
          else url=e.target.href||e.target.getParent().href;

          W.page(url, {data:params, method:'post',onComplete:function(){
              if(_finder) _finder.filter.value=params;
          }});
    }

    
    //$('select_shop').addEvent('change',function(e){setChartData(true);});
    dataAction.getElement('.filterBtn').addEvent('click',function(e){$('tohidden').set('html','');setChartData(true);});

    var packet=$('finder-packet-<{$name}>');

    if(packet) packet.addEvent("click",function(e){
      if(e.target.tagName.toLowerCase()=="a" || e.target.getParent().tagName.toLowerCase()=="a"){
        e.stop();
        $('tohidden').set('html','');
        setChartData(false,e);
      }
    });

    <{if $report == "month"}>
    function check_data(from,to){
      if(Date.parse(to.value.replace(/-/gi,"/"))<Date.parse(from.value.replace(/-/gi,"/"))){
        return MessageBox.error('<{t}>选择开始时间必须早于结束时间<{/t}>');
      }
      return true;
    }
    <{else}>
    function check_data(from,to){
      var data=[],
      _return=[from,to].every(function(el){
        if(!/^(19|20)[0-9]{2}-([1-9]|0[1-9]|1[012])-([1-9]|0[1-9]|[12][0-9]|3[01])+$/.test(el.value)){
          new MessageBox('<{t}>请录入日期格式yyyy-mm-dd<{/t}>',{type:'error',autohide:true});
          el.focus();
          return false;
        }
        data.push(Date.parse(el.value.replace(/-/gi,"/")));
        return true;
      });

      if(!_return)return null;
      if(data[1]<data[0]){
        return MessageBox.error('<{t}>选择开始时间必须早于结束时间<{/t}>');
      }
      return _return;
    }
    <{/if}>

    

    
  })();


  window.addEvent('domready',function(e){
    var lazyload = new LazyLoad();
    if($E('.chart-tabs li')) new ItemAgg($ES('.chart-tabs li'),$ES('.chart-view .chart-items'),{
      activeName:'current',
      itemsClass:'chart-item-cur',
      onActive:function(tab,item){
        lazyload.loadCustomLazyData(item,'textarea');
        var iframe=item.getElement('iframe'),
        src=item.retrieve('_src',iframe.src),
        params=$('dataAction').retrieve('_data','');
        if(window.ie||iframe.retrieve('_params','')!=params){
          iframe.src=src+params;
          iframe.store('_params',params);
        }
      }
    });
    if($ES('.finder-action .export')) $ES('.finder-action .export').each(function(el){
      el.addEvent('click',function(e){
            e.stop();
            var target = this.get('target'),options = JSON.decode(target);
            new Dialog(this.get('href'),$extend({
            ajaxoptions:{
              data:$('dataAction').toQueryString(),
              method: 'post'
            },
            onClose:function(){
              if((_finder=finderGroup['<{$name}>'])) _finder.refresh();
            }
            },options));
        });
    });

    if($E('.chart-view') && $E('.finder-options')){
      var closeBtn = $('btn_close_charts') || new Element('div.span-auto',{html:'<a id="btn_close_charts" href="javascript:void(0);">收起图形</a>'}).inject($E('.finder-options'));
      if(window.ie7) $E('.finder-options').style.cssText = 'width:auto;float:right;';
      var closedEl = $E('.chart-view');
      closeBtn.getElement('a').addEvent('click',function(){
        if(closedEl.isDisplay()){
          closedEl.hide();
          this.set('text','展开图形');
        }
        else{
          closedEl.show();
          var iframe=$E('.chart-view .chart-item-cur iframe'),
          params=$('dataAction').toQueryString();
          if(iframe && (window.ie||iframe.retrieve('_params','')!=params)){
            iframe.src += params;
            iframe.store('_params',params);
          }
          this.set('text','收起图形');
        }
        window.fireEvent('resize');
      });
    }
  });
</script>
