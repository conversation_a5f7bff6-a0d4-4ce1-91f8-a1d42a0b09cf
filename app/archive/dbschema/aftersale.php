<?php
$db['aftersale'] = array(
    'columns' => array(
        'aftersale_id'       => array(
            'type'     => 'int unsigned',
            'required' => true,
            'pkey'     => true,
            'editable' => false,
            'extra'    => 'auto_increment',
        ),
        'aftersale_bn'       => array(
            'type'            => 'varchar(32)',
            'required'        => true,
            'label'           => '售后单号',
            'is_title'        => true,
            'width'           => 125,
            'searchtype'      => 'has',
            'editable'        => false,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'shop_id'            => array(
            'type'            => 'table:shop@ome',
            'label'           => '店铺名称',
            'width'           => 120,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'order'           => 1,
        ),
        'shop_bn'            => array(
            'type'  => 'varchar(20)',
            'label' => '店铺编号',
        ),
        'shop_name'          => array(
            'type'  => 'varchar(255)',
            'label' => '店铺名称',
        ),
        'order_id'           => array(
            'type'       => 'table:orders@ome',
            'label'      => '订单号',
            'width'      => 140,
            'editable'   => false,
            'in_list'    => true,
            'searchtype' => 'has',
            'order'      => 2,
        ),
        'order_bn'           => array(
            'type'  => 'varchar(32)',
            'label' => '订单号',
        ),
        'return_id'          => array(
            'type'            => 'table:return_product@ome',
            'label'           => '售后申请单号',
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 3,
        ),
        'return_bn'          => array(
            'type'  => 'varchar(32)',
            'label' => '售后申请单号',
        ),
        'reship_id'          => array(
            'type'            => 'int unsigned',
            'label'           => '退换货单号',
            'editable'        => false,
            'in_list'         => false,
            'default_in_list' => false,
        ),
        'reship_bn'          => array(
            'type'  => 'varchar(64)',
            'label' => '退换货单号',
            'searchtype'      => 'has',
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'width'           => 140,
            'order'           => 4,
        ),
        'delivery_id'          => array(
            'type'            => 'table:delivery@ome',
            'label'           => '发货单号',
            'width'           => 140,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 4,
        ),
        'delivery_bn'          => array(
            'type'  => 'varchar(64)',
            'label' => '发货货单号',
        ),
        'return_apply_id'    => array(
            'type'            => 'table:refund_apply@ome',
            'label'           => '退款申请单号',
            'width'           => 140,
            'searchtype'      => 'has',
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 5,
        ),
        'return_apply_bn'    => array(
            'type'  => 'varchar(32)',
            'label' => '退款申请单号',
        ),
        'return_type'        => array(
            'type'            => array(
                'return' => '退货',
                'change' => '换货',
                'refund' => '退款',
                'refuse' => '追回',
            ),
            'label'           => '售后类型',
            'width'           => 95,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'order'           => 6,
        ),
        'refund_apply_money' => array(
            'type'            => 'money',
            'label'           => '退款申请金额',
            'width'           => 75,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
        ),
        'refundmoney'        => array(
            'type'            => 'money',
            'label'           => '已退款金额',
            'width'           => 75,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 7,
        ),
        'real_refund_amount' => array(
            'type' => 'money',
            'label' => '买家实退',
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'paymethod'          => array(
            'type'     => 'varchar(100)',
            'label'    => '退款支付方式',
            'width'    => 110,
            'editable' => false,
            #'filtertype' => 'normal',
            #'filterdefault' => true,
            'in_list'  => true,
        ),
        'member_id'          => array(
            'type'            => 'table:members@ome',
            'required'        => false,
            'editable'        => false,
            'label'           => '用户名',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 8,
            'width'           => 130,
        ),
        'member_uname'       => array(
            'type'     => 'varchar(255)',
            'label'    => '用户名',
            'required' => false,
        ),
        'ship_mobile'        => array(
            'type'            => 'varchar(200)',
            'required'        => false,
            'editable'        => false,
            'label'           => '手机号',
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'order'           => 9,
            'width'           => 130,
        ),
        'add_time'           => array(
            'type'            => 'time',
            'label'           => '售后申请时间',
            'width'           => 130,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'time',
            'filterdefault'   => true,
            'order'           => 10,
        ),
        'check_time'         => array(
            'type'            => 'time',
            'label'           => '审核时间',
            'width'           => 130,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'time',
            'filterdefault'   => true,
            'order'           => 11,
        ),
        'acttime'            => array(
            'type'            => 'time',
            'required'        => false,
            'editable'        => false,
            'label'           => '质检时间',
            'filterdefault'   => true,
            'filtertype'      => 'time',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 12,
            'width'           => 130,
        ),
        'refundtime'         => array(
            'type'            => 'time',
            'required'        => false,
            'editable'        => false,
            'label'           => '退款时间',
            'filterdefault'   => true,
            'filtertype'      => 'time',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 13,
            'width'           => 130,
        ),
        'check_op_id'        => array(
            'type'            => 'table:account@pam',
            'required'        => false,
            'editable'        => false,
            'label'           => '审核人',
            'in_list'         => true,
            'filterdefault'   => true,
            'filtertype'      => 'yes',
            'default_in_list' => true,
            'order'           => 14,
            'width'           => 130,
        ),
        'check_op_name'      => array(
            'type'  => 'varchar(32)',
            'label' => '审核人',
        ),
        'op_id'              => array(
            'type'            => 'table:account@pam',
            'required'        => false,
            'editable'        => false,
            'label'           => '质检人',
            'in_list'         => true,
            'filterdefault'   => true,
            'filtertype'      => 'yes',
            'default_in_list' => true,
            'order'           => 15,
            'width'           => 130,
        ),
        'op_name'            => array(
            'type'  => 'varchar(32)',
            'label' => '质检人',
        ),
        'refund_op_id'       => array(
            'type'            => 'table:account@pam',
            'required'        => false,
            'editable'        => false,
            'label'           => '退款人',
            'in_list'         => true,
            'filterdefault'   => true,
            'filtertype'      => 'yes',
            'default_in_list' => true,
            'order'           => 16,
            'width'           => 130,
        ),
        'refund_op_name'     => array(
            'type'  => 'varchar(32)',
            'label' => '退款人',
        ),
        'aftersale_time'     => array(
            'type'            => 'time',
            'required'        => true,
            'editable'        => false,
            'label'           => '售后单据创建时间',
            'filterdefault'   => true,
            'filtertype'      => 'time',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 13,
            'width'           => 130,
        ),
        'receiving_status'     => array(
            'type'            => [
                '0' => '待收货',
                '1' => '已收货',
            ],
            'default'         => '1',
            'label'           => '收货状态',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 18,
            'width'           => 130,
        ),
        'trigger_event'     => array(
            'type'            => [
                '1' => 'OMS退款',
                '2' => 'OMS收货',
                '3' => '平台退款',
            ],
            'default'         => '1',
            'label'           => '触发节点',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 20,
            'width'           => 130,
        ),
        'diff_order_bn'      => array(
            'type'            => 'varchar(32)',
            'label'           => '补差价订单',
            'filterdefault'   => true,
            'filtertype'      => 'normal',
            'in_list'         => true,
            'default_in_list' => true,
            'width'           => 130,
        ),
        'change_order_bn'    => array(
            'type'            => 'varchar(32)',
            'label'           => '换货订单号',
            'filterdefault'   => true,
            'filtertype'      => 'normal',
            'in_list'         => true,
            'default_in_list' => true,
            'width'           => 130,
        ),
        'pay_type'           => array(
            'type'     => array(
                'online'  => '在线支付',
                'offline' => '线下支付',
                'deposit' => '预存款支付',
            ),
            'default'  => 'online',
            'label'    => '支付类型',
            'width'    => 110,
            'editable' => false,
        ),
        'account'            => array(
            'type'     => 'varchar(50)',
            'editable' => false,
            'in_list'  => true,
            'label'    => '退款帐号',
        ),
        'bank'               => array(
            'type'     => 'varchar(50)',
            'editable' => false,
            'label'    => '退款银行',
        ),
        'pay_account'        => array(
            'type'     => 'varchar(50)',
            'editable' => false,
            'label'    => '收款帐号',
        ),
        'refund_apply_time'  => array(
            'type'          => 'time',
            'editable'      => false,
            'label'         => '退款申请时间',
            'filtertype'    => 'time',
            'filterdefault' => true,
        ),
        'problem_name'       => array(
            'type'            => 'varchar(200)',
            'label'           => '售后服务类型',
            'filterdefault'   => true,
            'filtertype'      => 'normal',
            'in_list'         => true,
            'default_in_list' => true,
            'width'           => 130,
        ),
        'archive'            => array(
            'type'            => 'tinyint unsigned',
            'required'        => true,
            'default'         => 0,
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'label'           => '来源',
        ),
        'org_id'             => array(
            'type'            => 'table:operation_organization@ome',
            'label'           => '运营组织',
            'editable'        => false,
            'width'           => 60,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'in_list'         => true,
            'default_in_list' => true,
        ),
        'platform_order_bn' =>array (
            'type' => 'varchar(32)',
            'label' => '平台订单号',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'normal',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'settlement_amount' => array(
            'type'    => 'money',
            'default' => '0',
            'label'   => '结算金额',//客户实付 + 平台支付总额
        ),
        'platform_amount' => array(
            'type'    => 'money',
            'default' => '0',
            'label'   => '平台承担金额（不包含支付优惠）',
        ),
        'actually_amount' => array(
            'type'    => 'money',
            'default' => '0',
            'label'   => '客户实付',// 已支付金额 减去平台支付优惠，加平台支付总额
        ),
        'platform_pay_amount' => array(
            'type'    => 'money',
            'default' => '0',
            'label'   => '支付优惠金额',
        ),
        'ship_time' => array(
            'type'     => 'time',
            'label'    => '发货时间',
            'editable' => false,
            'width'    => 130,
        ),
        'need_refundmoney' =>
            array(
                'type' => 'money',
                'label' => '需退款金额',//(协同版)
                'width' => 75,
                'editable' => false,
                'in_list' => true,
                'default_in_list' => true,
                'order'=>8,
            ),
        'order_bool_type' =>
            array (
                'type' => 'varchar(25)',
                'label' => '交易分类',//(协同版)
                'editable' => false,
            ),
        'extra_info'=>array(
            'type'=>'varchar(25)',
            'label'=>'交易分类编码',//(协同版)
            'filtertype' => 'yes',
            'filterdefault' => true,
            'in_list'=>true,
            'default_in_list'=>true,
        
        ),
        'at_time'       => array(
            'type'            => 'TIMESTAMP',
            'label'           => '创建时间',
            'default_in_list' => false,
            'in_list'         => true,
            'default'         => 'CURRENT_TIMESTAMP',
        ),
        'up_time'       => array(
            'type'            => 'TIMESTAMP',
            'label'           => '更新时间',
            'default_in_list' => false,
            'in_list'         => true,
            'default'         => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'filtertype'      => 'normal',
            'filterdefault'   => true,
        ),
        'betc_id' => array(
            'type' => 'int unsigned',
            'default' => 0,
            'editable' => false,
            'label' => '贸易公司ID',
        ),
        'cos_id' => array(
            'type' => 'int unsigned',
            'default' => 0,
            'editable' => false,
            'label' => '组织架构ID',
        ),
        'archive_time' =>
            array (
                'type' => 'int',
                'label' => '归档时间',
                'required' => true,
                'editable' => false,
                'in_list' => true,
                'default_in_list' => true,
            ),
    ),
    'index' => array(
        'ind_aftersale_time' => array(
            'columns' => array(
                0 => 'aftersale_time',
            ),
        ),
        'idx_c_reship_id'     => array(
            'columns' => array(
                0 => 'reship_id',
            ),
            'prefix'  => 'unique',
        ),
        'ind_refundtime'     => array(
            'columns' => array(
                0 => 'refundtime',
            ),
        ),
        'ind_return_type'     => array(
            'columns' => array(
                0 => 'return_type',
            ),
        ),
        'ind_acttime'        => array(
            'columns' => array(
                0 => 'acttime',
            ),
        ),
        'ind_check_time'     => array(
            'columns' => array(
                0 => 'check_time',
            ),
        ),
        'ind_add_time'       => array(
            'columns' => array(
                0 => 'add_time',
            ),
        ),
        'ind_platform_order_bn' => array(
            'columns' => array(
                0 => 'platform_order_bn',
            ),
        ),
        'idx_at_time'           => array(
            'columns' => array(
                0 => 'at_time'
            )
        ),
        'idx_up_time'           => array(
            'columns' => array(
                0 => 'up_time'
            )
        ),
        'ind_ship_time'         => array(
            'columns' => array(
                0 => 'ship_time',
            ),
        ),
        'ind_betc_id' => array(
            'columns' => array(
                0 => 'betc_id',
            ),
        ),
        'ind_cos_id' => array(
            'columns' => array(
                0 => 'cos_id',
            ),
        ),
    ),
    'engine'  => 'innodb',
    'version' => '$Rev: 41996 $',
    'comment' => '售后单据',
    'charset' => 'utf8mb4',
);
