<?php

$db['order_objects_check_items'] = array(
    'columns' => array(
        'check_item_id' => array(
            'type' => 'int unsigned',
            'required' => true,
            'pkey' => true,
            'extra' => 'auto_increment',
            'editable' => false,
        ),
        'order_id' => array(
            'type' => 'int unsigned',
            'label' => '订单id',
            'comment' => '订单id',
            'editable' => false,
            'width' => 200,
            'in_list' => false,
            'default_in_list' => false,
        ),
        'object_comp_key' => array(
            'type' => 'varchar(50)',
            'default' => '',
            'editable' => false,
        ),
        'obj_type' => array(
            'type' => 'varchar(50)',
            'default' => '',
            'editable' => false,
        ),
        'shop_goods_id' => array(
            'type' => 'varchar(50)',
            'default' => 0,
            'editable' => false,
        ),
        'bn' => array(
            'type' => 'varchar(40)',
            'editable' => false,
            'is_title' => true,
        ),
        'channel' => array(
            'type' => 'varchar(32)',
            'label' => '会员反馈途径',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'problem_desc' => array(
            'type' => 'varchar(32)',
            'label' => '问题描述',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'order_label' => array(
            'type' => 'varchar(32)',
            'label' => '订单标签',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'image_fileid_list' => array(
            'type' => 'varchar(255)',
            'label' => '图片信息',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'image_list' => array(
            'type' => 'varchar(255)',
            'label' => '图片信息',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'video_fileid_list' => array(
            'type' => 'varchar(255)',
            'label' => '视频信息',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'video_list' => array(
            'type' => 'varchar(255)',
            'label' => '视频信息',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'delivery_warehouse' => array(
            'type' => 'varchar(32)',
            'label' => '发货仓',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'order_sn' => array(
            'type' => 'varchar(32)',
            'label' => '订单号',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'first_classification' => array(
            'type' => 'varchar(64)',
            'label' => '一级分类',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'second_classification' => array(
            'type' => 'varchar(64)',
            'label' => '二级分类',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'third_classification' => array(
            'type' => 'varchar(64)',
            'label' => '三级分类',
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'textarea',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => false,
        ),
        'at_time' => array(
            'type' => 'TIMESTAMP',
            'label' => '创建时间',
            'default' => 'CURRENT_TIMESTAMP',
            'width' => 120,
            'in_list' => false,
        ),
        'up_time' => array(
            'type' => 'TIMESTAMP',
            'label' => '更新时间',
            'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'width' => 120,
            'in_list' => false,
        ),
        'archive_time' => array(
            'type' => 'int unsigned',
            'label' => '归档时间',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
            'filtertype' => 'time',
            'filterdefault' => true,
        ),
    ),
    'index' => array(
        'ind_order_id' => array(
            'columns' => array(
                0 => 'order_id',
            ),
        ),
        'ind_obj_type' => array(
            'columns' => array(
                0 => 'obj_type',
            ),
        ),
        'ind_shop_goods_id' => array(
            'columns' => array(
                0 => 'shop_goods_id',
            ),
        ),
        'ind_bn' => array(
            'columns' => array(
                0 => 'bn',
            ),
        ),
        'ind_archive_time' => array(
            'columns' => array(
                0 => 'archive_time',
            ),
        ),
    ),
    'engine' => 'innodb',
    'version' => '$Rev: 40912 $',
    'comment' => '归档订单对象检测表',
); 