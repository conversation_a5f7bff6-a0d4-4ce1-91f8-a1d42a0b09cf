-- 任务重试与回调监控优化 - 数据库表结构修改
-- 为相关表添加 retry_count 字段以支持重试机制
-- 执行日期: 2025-08-04
-- 作者: AI Assistant

-- 为 sap_so 表添加重试计数字段
-- 按照 docs/cheatsheet/database/schema-config.md 规范添加字段
ALTER TABLE `sdb_miele_sap_so`
ADD COLUMN `retry_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重试次数，用于定时任务重试机制'
AFTER `sap_rsv_time`;

-- 为 sap_odn 表添加重试计数字段
-- 按照 docs/cheatsheet/database/schema-config.md 规范添加字段
ALTER TABLE `sdb_miele_sap_odn`
ADD COLUMN `retry_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重试次数，用于定时任务重试机制'
AFTER `sap_consign_msg`;

-- 为 retry_count 字段添加索引以提高查询性能
ALTER TABLE `sdb_miele_sap_so` 
ADD INDEX `ind_retry_count` (`retry_count`);

ALTER TABLE `sdb_miele_sap_odn` 
ADD INDEX `ind_retry_count` (`retry_count`);

-- 为现有记录设置默认重试次数为0（如果有数据的话）
UPDATE `sdb_miele_sap_so` SET `retry_count` = 0 WHERE `retry_count` IS NULL;
UPDATE `sdb_miele_sap_odn` SET `retry_count` = 0 WHERE `retry_count` IS NULL;

-- 验证字段添加是否成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('sdb_miele_sap_so', 'sdb_miele_sap_odn')
    AND COLUMN_NAME = 'retry_count';

-- 验证索引添加是否成功
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('sdb_miele_sap_so', 'sdb_miele_sap_odn')
    AND INDEX_NAME = 'ind_retry_count';

-- 回滚脚本（如果需要的话）
/*
-- 删除索引
ALTER TABLE `sdb_miele_sap_so` DROP INDEX `ind_retry_count`;
ALTER TABLE `sdb_miele_sap_odn` DROP INDEX `ind_retry_count`;

-- 删除字段
ALTER TABLE `sdb_miele_sap_so` DROP COLUMN `retry_count`;
ALTER TABLE `sdb_miele_sap_odn` DROP COLUMN `retry_count`;
*/
